# 通用Excel数据导入MySQL程序

这是一个基于Python开发的通用Excel数据导入MySQL程序，支持多Sheet处理、中文转拼音、数据类型自动识别、智能数据更新等功能。

## 功能特性

### 🚀 核心功能
- **多Sheet支持**: 自动处理Excel文件中的所有Sheet，每个Sheet生成一张MySQL表
- **中文转拼音**: 自动将中文Sheet名转换为符合MySQL规范的拼音表名
- **智能数据类型识别**: 自动识别数据类型（数值型用FLOAT，时间用DATETIME，文本用VARCHAR）
- **智能数据更新**: 比较Excel数据与数据库表的时间字段，相同时间数据用Excel替换，其他数据直接添加
- **动态表结构**: 根据Excel列名和数据类型动态创建MySQL表结构，自动添加id主键

### 📊 数据处理特性
- **批量插入**: 支持大数据量的批量插入，提高导入效率
- **数据验证**: 导入后可验证数据完整性
- **错误处理**: 完善的错误处理和日志记录
- **预览功能**: 导入前可预览Excel文件结构

## 安装依赖

```bash
pip install pandas mysql-connector-python openpyxl
```

## 配置说明

### 数据库配置 (db_config.py)

```python
# MySQL数据库配置
DB_CONFIG = {
    'host': 'localhost',        # MySQL服务器地址
    'port': 3306,              # MySQL端口号
    'database': 'agent',       # 数据库名称
    'user': 'root',            # 用户名
    'password': '1234',        # 密码
    'charset': 'utf8mb4',      # 字符集
    'autocommit': False,       # 自动提交
    'use_unicode': True        # 使用Unicode
}

# Excel文件路径配置
EXCEL_FILE_PATH = '../water-data.xlsx'  # 默认Excel文件路径
```

### 导入配置

```python
IMPORT_CONFIG = {
    'batch_size': 1000,        # 批量插入的数据条数
    'max_varchar_length': 255, # VARCHAR字段的最大长度
    'datetime_columns': ['date', 'time', 'datetime', '时间', '日期'],  # 时间列关键词
    'skip_empty_sheets': True, # 是否跳过空的Sheet
    'create_index_on_datetime': True,  # 是否在时间字段上创建索引
}
```

## 使用方法

### 1. 命令行使用

```bash
# 基本使用（交互式）
python main.py

# 指定Excel文件
python main.py --excel /path/to/your/file.xlsx

# 预览Excel文件结构（不导入数据）
python main.py --excel /path/to/your/file.xlsx --preview

# 静默模式导入
python main.py --excel /path/to/your/file.xlsx --quiet

# 导入后验证数据
python main.py --excel /path/to/your/file.xlsx --verify
```

### 2. 程序化使用

```python
from universal_excel_importer import UniversalExcelImporter

# 创建导入器
importer = UniversalExcelImporter(excel_path='your_file.xlsx')

# 执行导入
success = importer.import_all_sheets()

if success:
    print("导入成功！")
    # 验证导入结果
    importer.verify_import()
else:
    print("导入失败！")
```

## 数据类型映射

| Excel数据类型 | MySQL数据类型 | 说明 |
|--------------|--------------|------|
| 整数 | TINYINT/SMALLINT/INT/BIGINT | 根据数值范围自动选择 |
| 浮点数 | FLOAT | 所有小数都使用FLOAT |
| 日期时间 | DATETIME | 自动识别时间格式 |
| 布尔值 | BOOLEAN | True/False值 |
| 文本 | VARCHAR/TEXT/LONGTEXT | 根据长度自动选择 |

## 表结构说明

每个生成的MySQL表都包含以下字段：
- `id`: 自增主键
- `[原Excel列名的拼音]`: 对应Excel的数据列
- `created_at`: 记录创建时间
- `updated_at`: 记录更新时间

## 智能更新逻辑

1. **时间列识别**: 自动识别包含时间数据的列
2. **重复检测**: 比较Excel数据与数据库中的时间数据
3. **智能替换**: 对于相同时间点的数据，用Excel数据替换数据库中的数据
4. **增量添加**: 对于新的时间点，直接添加到数据库

## 中文转拼音规则

- 支持常用中文字符转拼音
- 特殊字符转换为下划线
- 确保表名和列名符合MySQL命名规范
- 自动处理重复和冲突

## 错误处理

- 完整的异常捕获和处理
- 详细的日志记录
- 数据库事务回滚机制
- 用户友好的错误提示

## 示例

假设有一个Excel文件包含以下Sheet：
- "水位数据" -> 生成表 `shui_wei_shu_ju`
- "温度记录" -> 生成表 `wen_du_ji_lu`
- "流量统计" -> 生成表 `liu_liang_tong_ji`

每个表都会根据Excel的列结构自动创建，并支持智能数据更新。

## 注意事项

1. **数据库权限**: 确保MySQL用户有创建表和插入数据的权限
2. **Excel格式**: 支持.xlsx和.xls格式，第一行必须是列名
3. **内存使用**: 大文件导入时注意内存使用情况
4. **字符编码**: 使用UTF-8编码处理中文字符
5. **时间格式**: 支持多种时间格式的自动识别

## 故障排除

### 常见问题

1. **连接数据库失败**
   - 检查数据库配置信息
   - 确认MySQL服务是否启动
   - 验证用户名和密码

2. **Excel文件读取失败**
   - 确认文件路径正确
   - 检查文件是否被其他程序占用
   - 验证Excel文件格式

3. **中文转拼音问题**
   - 程序内置常用中文字符映射
   - 未映射字符会使用ASCII码转换
   - 可以扩展PINYIN_MAP字典

4. **数据类型识别错误**
   - 检查Excel数据格式
   - 可以手动调整detect_column_type函数
   - 确保时间列包含有效的时间数据

## 扩展开发

程序采用模块化设计，可以轻松扩展：

- `utils.py`: 工具函数，可添加更多数据处理功能
- `universal_excel_importer.py`: 核心导入逻辑
- `db_config.py`: 配置文件
- `main.py`: 主程序入口

欢迎根据需要进行定制和扩展！
