# test_sch_mcp.py - 测试三峡水利调度 MCP 服务器

import asyncio
import json
from sch_mcp import mcp

async def test_mcp_tools():
    """测试MCP工具的功能"""
    print("开始测试三峡水利调度 MCP 工具...")
    
    # 测试1: 获取默认参数
    print("\n=== 测试1: 获取默认参数 ===")
    try:
        result = await mcp.get_default_parameters()
        print("默认参数获取成功:")
        print(result)
    except Exception as e:
        print(f"获取默认参数失败: {e}")
    
    # 测试2: 列出可用场景
    print("\n=== 测试2: 列出可用场景 ===")
    try:
        result = await mcp.list_available_scenarios()
        print("可用场景列表:")
        print(result)
    except Exception as e:
        print(f"获取可用场景失败: {e}")
    
    # 测试3: 验证特定场景数据
    print("\n=== 测试3: 验证场景数据 ===")
    try:
        result = await mcp.validate_scenario_data("1954", "0.01")
        print("场景数据验证结果:")
        print(result)
    except Exception as e:
        print(f"验证场景数据失败: {e}")
    
    # 测试4: 执行简单的调度计算
    print("\n=== 测试4: 执行调度计算 ===")
    try:
        scenarios = [
            {"year": "1954", "frequency": "0.01"}
        ]
        result = await mcp.sanxia_flood_routing(
            scenarios=scenarios,
            init_water_level=161.0,
            upstream_max=70.0,
            yl_boolean=False
        )
        print("调度计算结果:")
        # 只打印结果的摘要部分，避免输出过长
        result_dict = json.loads(result)
        if result_dict.get("success"):
            print(f"计算成功: {result_dict.get('message')}")
            print(f"参数: {result_dict.get('parameters_used')}")
            print(f"汇总: {result_dict.get('summary')}")
        else:
            print(f"计算失败: {result_dict.get('error')}")
    except Exception as e:
        print(f"执行调度计算失败: {e}")

if __name__ == "__main__":
    asyncio.run(test_mcp_tools())
