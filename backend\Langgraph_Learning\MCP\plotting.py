# plotting_mcp.py

import os
import matplotlib

matplotlib.use('Agg')  # 设置非交互式后端
import matplotlib.pyplot as plt
import seaborn as sns
import pandas as pd
from mcp.server.fastmcp import FastMCP
from pydantic import BaseModel, Field
from io import StringIO
from typing import Annotated

# 初始化 MCP 服务器
mcp = FastMCP("PlottingServer")

# --- 图像保存配置 ---
# 确保这个路径对于运行此脚本的用户是可写的
# 建议使用绝对路径或相对于脚本位置的路径
# public 文件夹通常用于存放前端可以访问的静态资源
# BASE_DIR = os.path.join(os.getcwd(), "public")
BASE_DIR = r"G:\Study\Vue\agent-chat-ui-main\public"
IMAGES_DIR = os.path.join(BASE_DIR, "images")
os.makedirs(IMAGES_DIR, exist_ok=True)


@mcp.tool()
# async def fig_inter(py_code: str, data_json: str, fname: str) -> str:
async def fig_inter(
    py_code: Annotated[str, Field(description="要执行的 Python 绘图代码。代码中应使用名为 'df' 的 pandas DataFrame")],
    data_json: Annotated[str, Field(description="包含绘图所需数据的 JSON 字符串（应为 'records' 格式）")],
    fname: Annotated[str, Field(description="图像对象的变量名（如 'fig'），以及保存的文件名（不含扩展名）")]
) -> str:
    """
    使用提供的数据（JSON格式）执行 Python 绘图代码，并保存生成的图像。
    注意：
    1. 绘图代码必须引用一个名为 `df` 的 pandas DataFrame，该 DataFrame 会从此工具的 `data_json` 参数自动加载。
    2. 代码必须创建一个图像对象并赋值给指定变量名（`fig`）。
    3. 必须使用 `fig = plt.figure()` 或 `fig, ax = plt.subplots()`。
    4. 不要使用 `plt.show()`。
    5. 请确保代码调用 `fig.tight_layout()`。
    6. 调用工具时，直接将 fname 参数的值设置为 'fig'
    6. 所有绘图代码中，坐标轴标签（xlabel、ylabel）、标题（title）、图例（legend）等文本内容，必须使用英文描述。
    :param py_code: 用于绘图的 Python 代码。
    :param data_json: 'records' 格式的 JSON 字符串数据。
    :param fname: 绘图对象变量名和输出文件名。
    :return: 成功时返回图片的相对路径，失败时返回错误信息。
    """
    print(f"正在调用 fig_inter 工具生成图像 '{fname}.png'...")

    # 将传入的 JSON 数据加载到 DataFrame
    try:
        data_io = StringIO(data_json)
        df = pd.read_json(data_io, orient='records')
    except Exception as e:
        return f"❌ JSON 数据解析失败: {e}"

    local_vars = {"plt": plt, "pd": pd, "sns": sns, "df": df}

    try:
        # 执行用户提供的绘图代码
        exec(py_code, {}, local_vars)

        # --- 添加这行用于调试 ---
        print(f"调试信息：exec后，local_vars包含的键有: {list(local_vars.keys())}")

        fig = local_vars.get(fname)
        if fig and isinstance(fig, plt.Figure):
            image_filename = f"{fname}.png"
            abs_path = os.path.join(IMAGES_DIR, image_filename)
            rel_path = os.path.join("images", image_filename).replace("\\", "/")  # 保证前端路径格式

            fig.savefig(abs_path, bbox_inches='tight')
            plt.close(fig)  # 关闭图像，释放内存

            print(f"图片已保存至: {abs_path}")
            # 返回给前端使用的相对路径
            return f"✅ 图片已生成，路径为: ![{fname}]({rel_path})"
        else:
            return f"⚠️ 图像对象 '{fname}' 未找到或类型不正确。请确保代码正确创建了 matplotlib.figure.Figure 对象。"
    except Exception as e:
        return f"❌ 绘图代码执行失败: {e}"
    finally:
        plt.close('all')  # 清理所有可能未关闭的图像


if __name__ == "__main__":
    print(f"绘图 MCP 服务已启动，图片将保存到: {IMAGES_DIR}")
    mcp.run(transport='stdio')