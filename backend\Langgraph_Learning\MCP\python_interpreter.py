# python_interpreter_mcp.py

from mcp.server.fastmcp import FastMCP
from pydantic import BaseModel, Field
import pandas as pd
import json
from io import StringIO
from typing import Annotated

# 初始化 MCP 服务器
mcp = FastMCP("PythonInterpreterServer")

# 这个字典将在此 MCP 服务的生命周期内保持状态
# 允许在多次调用之间共享变量
persistent_scope = {"pd": pd, "json": json, "StringIO": StringIO}

# Python代码执行工具结构化参数说明
# class PythonCodeInput(BaseModel):
#     py_code: str = Field(description="一段合法的 Python 代码字符串，例如 '2 + 2' 或 'x = 3\\ny = x * 2'")

@mcp.tool()
async def python_inter(py_code: Annotated[str, Field(description="一段合法的、非绘图的 Python 代码字符串，例如 '2 + 2' 或 'x = 3\\ny = x * 2'")]
) -> str:
# async def python_inter(py_code: str) -> str:
    """
    执行一段非绘图类的 Python 代码并返回结果。
    这个服务是有状态的：你可以在一次调用中定义变量，并在后续调用中使用它。
    例如，第一次调用 `df = pd.read_json(...)`，第二次调用 `df.head()`。
    :param py_code: 要执行的 Python 代码。
    :return: 代码执行的输出、表达式的结果或成功/失败消息。
    """
    print(f"正在执行 Python 代码: {py_code}")
    global persistent_scope

    try:
        # 尝试将代码作为表达式求值
        result = eval(py_code, persistent_scope)
        # 如果结果是 DataFrame，返回其摘要信息
        if isinstance(result, pd.DataFrame):
            return f"DataFrame Info:\n{str(result.info())}\n\nHead:\n{result.head().to_string()}"
        return str(result)
    except Exception:
        # 如果作为表达式失败，则作为语句执行
        try:
            vars_before = set(persistent_scope.keys())
            exec(py_code, persistent_scope)
            vars_after = set(persistent_scope.keys())
            new_vars = vars_after - vars_before

            if new_vars:
                # 如果创建了新变量，返回新变量的信息
                new_var_values = {k: str(type(persistent_scope[k])) for k in new_vars}
                return f"代码执行成功。创建了新变量: {new_var_values}"
            else:
                return "代码执行成功，无新变量创建。"
        except Exception as e:
            return f"❌ 代码执行时报错: {e}"


if __name__ == "__main__":
    print("Python 代码执行 MCP 服务已启动...")
    mcp.run(transport='stdio')