"""
测试Word编辑Agent的功能
验证前后端集成和Word编辑流程
"""

import asyncio
import json
import logging
from pathlib import Path
import sys
import os

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from graph import make_graph

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

async def test_word_editing_workflow():
    """测试完整的Word编辑工作流程"""
    
    logger.info("🚀 开始测试Word编辑Agent工作流程...")
    
    try:
        # 1. 创建Agent
        logger.info("1️⃣ 创建LangGraph Agent...")
        agent = await make_graph()
        logger.info("✅ Agent创建成功")
        
        # 2. 测试加载和分割文档
        logger.info("2️⃣ 测试文档加载和分割...")
        
        test_messages = [
            {
                "role": "user", 
                "content": "请帮我加载一个Word文档并进行章节分割。文档路径是 '/test/sample_document.docx'，每个章节最大30000个Token。"
            }
        ]
        
        config = {"configurable": {"thread_id": "test_word_editing_1"}}
        
        response = await agent.ainvoke(
            {"messages": test_messages},
            config=config
        )
        
        logger.info("📄 文档加载响应:")
        for msg in response["messages"]:
            if msg.type == "ai":
                logger.info(f"AI: {msg.content}")
        
        # 3. 测试章节选择
        logger.info("3️⃣ 测试章节选择...")
        
        test_messages.append({
            "role": "user",
            "content": "请帮我选择第一个章节进行编辑。"
        })
        
        response = await agent.ainvoke(
            {"messages": test_messages},
            config=config
        )
        
        logger.info("📝 章节选择响应:")
        for msg in response["messages"]:
            if msg.type == "ai" and msg not in [m for m in test_messages if m.get("type") == "ai"]:
                logger.info(f"AI: {msg.content}")
        
        # 4. 测试章节编辑
        logger.info("4️⃣ 测试章节编辑...")
        
        test_messages.append({
            "role": "user",
            "content": "请在当前章节添加一个段落，内容是：'这是通过AI助手添加的新段落，用于测试Word文档编辑功能。'"
        })
        
        response = await agent.ainvoke(
            {"messages": test_messages},
            config=config
        )
        
        logger.info("✏️ 章节编辑响应:")
        for msg in response["messages"]:
            if msg.type == "ai" and msg not in [m for m in test_messages if m.get("type") == "ai"]:
                logger.info(f"AI: {msg.content}")
        
        # 5. 测试保存文档
        logger.info("5️⃣ 测试保存文档...")
        
        test_messages.append({
            "role": "user",
            "content": "请保存文档更改。"
        })
        
        response = await agent.ainvoke(
            {"messages": test_messages},
            config=config
        )
        
        logger.info("💾 文档保存响应:")
        for msg in response["messages"]:
            if msg.type == "ai" and msg not in [m for m in test_messages if m.get("type") == "ai"]:
                logger.info(f"AI: {msg.content}")
        
        logger.info("🎉 Word编辑工作流程测试完成！")
        
    except Exception as e:
        logger.error(f"❌ 测试过程中发生错误: {e}")
        raise

async def test_word_section_management():
    """测试Word章节管理功能"""
    
    logger.info("🔍 开始测试Word章节管理功能...")
    
    try:
        agent = await make_graph()
        
        # 测试获取文档章节信息
        test_messages = [
            {
                "role": "user",
                "content": "请帮我查看已加载文档的所有章节信息，包括每个章节的标题、字数和Token数量。"
            }
        ]
        
        config = {"configurable": {"thread_id": "test_section_management"}}
        
        response = await agent.ainvoke(
            {"messages": test_messages},
            config=config
        )
        
        logger.info("📊 章节信息响应:")
        for msg in response["messages"]:
            if msg.type == "ai":
                logger.info(f"AI: {msg.content}")
        
        logger.info("✅ 章节管理功能测试完成！")
        
    except Exception as e:
        logger.error(f"❌ 章节管理测试失败: {e}")
        raise

async def test_word_formatting():
    """测试Word格式化功能"""
    
    logger.info("🎨 开始测试Word格式化功能...")
    
    try:
        agent = await make_graph()
        
        # 测试文本格式化
        test_messages = [
            {
                "role": "user",
                "content": "请帮我将当前章节中的第一段文字设置为粗体，并将标题设置为蓝色。"
            }
        ]
        
        config = {"configurable": {"thread_id": "test_formatting"}}
        
        response = await agent.ainvoke(
            {"messages": test_messages},
            config=config
        )
        
        logger.info("🎨 格式化响应:")
        for msg in response["messages"]:
            if msg.type == "ai":
                logger.info(f"AI: {msg.content}")
        
        logger.info("✅ 格式化功能测试完成！")
        
    except Exception as e:
        logger.error(f"❌ 格式化测试失败: {e}")
        raise

async def test_error_handling():
    """测试错误处理"""
    
    logger.info("⚠️ 开始测试错误处理...")
    
    try:
        agent = await make_graph()
        
        # 测试无效文档路径
        test_messages = [
            {
                "role": "user",
                "content": "请加载一个不存在的文档：'/invalid/path/nonexistent.docx'"
            }
        ]
        
        config = {"configurable": {"thread_id": "test_error_handling"}}
        
        response = await agent.ainvoke(
            {"messages": test_messages},
            config=config
        )
        
        logger.info("🚫 错误处理响应:")
        for msg in response["messages"]:
            if msg.type == "ai":
                logger.info(f"AI: {msg.content}")
        
        logger.info("✅ 错误处理测试完成！")
        
    except Exception as e:
        logger.error(f"❌ 错误处理测试失败: {e}")
        # 这里不抛出异常，因为我们期望有错误

async def run_all_tests():
    """运行所有测试"""
    
    logger.info("🧪 开始运行Word编辑Agent集成测试套件...")
    
    tests = [
        ("Word编辑工作流程", test_word_editing_workflow),
        ("Word章节管理", test_word_section_management),
        ("Word格式化", test_word_formatting),
        ("错误处理", test_error_handling),
    ]
    
    passed = 0
    failed = 0
    
    for test_name, test_func in tests:
        try:
            logger.info(f"\n{'='*50}")
            logger.info(f"🔬 运行测试: {test_name}")
            logger.info(f"{'='*50}")
            
            await test_func()
            
            logger.info(f"✅ 测试通过: {test_name}")
            passed += 1
            
        except Exception as e:
            logger.error(f"❌ 测试失败: {test_name} - {e}")
            failed += 1
    
    logger.info(f"\n{'='*50}")
    logger.info(f"📊 测试结果汇总")
    logger.info(f"{'='*50}")
    logger.info(f"✅ 通过: {passed}")
    logger.info(f"❌ 失败: {failed}")
    logger.info(f"📈 成功率: {(passed/(passed+failed)*100):.1f}%")
    
    if failed == 0:
        logger.info("🎉 所有测试都通过了！Word编辑Agent集成成功！")
    else:
        logger.warning(f"⚠️ 有 {failed} 个测试失败，需要进一步调试。")
    
    return passed, failed

if __name__ == "__main__":
    # 运行测试
    asyncio.run(run_all_tests())
