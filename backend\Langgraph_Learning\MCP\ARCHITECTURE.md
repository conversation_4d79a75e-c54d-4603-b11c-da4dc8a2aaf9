# 三峡水利调度 MCP 集成架构

## 整体架构

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   LangGraph     │    │   数据库 MCP     │    │   调度 MCP      │
│     Agent       │◄──►│    服务器       │    │    服务器       │
│                 │    │                 │    │                 │
│ - 对话理解      │    │ - Excel数据读取  │    │ - 调度计算      │
│ - 任务协调      │    │ - 数据库查询    │    │ - 参数验证      │
│ - 结果展示      │    │ - 数据转换      │    │ - 结果分析      │
└─────────────────┘    └─────────────────┘    └─────────────────┘
         │                       │                       │
         └───────────────────────┼───────────────────────┘
                                 │
                    ┌─────────────────┐
                    │  flood_control  │
                    │     模块        │
                    │                 │
                    │ - 核心算法      │
                    │ - 数据处理      │
                    │ - 配置管理      │
                    └─────────────────┘
```

## 设计原则

### 1. 模块化分离
- **数据层**: 数据库MCP负责所有数据获取
- **计算层**: 调度MCP专注于计算逻辑
- **交互层**: LangGraph Agent处理用户交互

### 2. 参数化配置
- **用户参数**: init_water_level, upstream_max, yl_boolean
- **默认参数**: 其他所有参数使用input.json中的默认值
- **场景驱动**: 支持单个或多个场景的灵活计算

### 3. 错误处理
- **数据验证**: 计算前验证数据可用性
- **异常捕获**: 详细的错误信息和建议
- **优雅降级**: 部分场景失败不影响其他场景

## 文件结构

```
Langgraph_Learning/MCP/
├── sch_mcp.py                    # 主MCP服务器
├── test_sch_mcp.py              # 测试脚本
├── example_agent_with_sch_mcp.py # Agent集成示例
├── langgraph_config.json        # LangGraph配置
├── README_sch_mcp.md           # 使用说明
└── ARCHITECTURE.md             # 架构文档

flood_control/
├── sanxia_scheduling.py        # 核心计算模块（不修改）
└── parameter/
    ├── input.json             # 默认配置
    └── 大洪水/
        └── 洪水过程.xlsx      # 洪水数据
```

## 工作流程

### 1. 典型用户交互

```
用户: "计算1954年0.01频率的洪水调度，初始水位165米"
  ↓
Agent: 解析用户意图
  ↓
Agent: 调用 validate_scenario_data("1954", "0.01")
  ↓
Agent: 调用 sanxia_flood_routing([{"year":"1954","frequency":"0.01"}], 165)
  ↓
Agent: 解析计算结果并向用户展示
```

### 2. 数据流

```
用户输入 → Agent解析 → MCP工具调用 → 核心算法 → 结果返回 → Agent展示
```

## MCP工具详解

### 1. sanxia_flood_routing
- **功能**: 执行主要的调度计算
- **输入**: 场景列表 + 可选参数
- **输出**: 完整的计算结果和汇总信息
- **特点**: 支持批量计算多个场景

### 2. get_default_parameters
- **功能**: 获取模型默认参数
- **输入**: 无
- **输出**: 参数配置和说明
- **用途**: 帮助用户了解可调整的参数

### 3. validate_scenario_data
- **功能**: 验证数据可用性
- **输入**: 年份和频率
- **输出**: 验证结果和数据信息
- **用途**: 计算前的数据检查

### 4. list_available_scenarios
- **功能**: 列出所有可用场景
- **输入**: 无
- **输出**: 可用年份和频率列表
- **用途**: 帮助用户选择合适的场景

## 集成方式

### 1. 本地开发
```python
# 直接导入使用
from sch_mcp import mcp
result = await mcp.sanxia_flood_routing(scenarios)
```

### 2. MCP客户端
```python
# 通过MCP协议使用
client = MultiServerMCPClient({
    "scheduling": {
        "command": "python",
        "args": ["./sch_mcp.py"],
        "transport": "stdio"
    }
})
tools = await client.get_tools()
```

### 3. LangGraph Agent
```python
# 集成到Agent中
agent = create_react_agent(model, tools)
response = await agent.ainvoke({"messages": [...]})
```

## 扩展性

### 1. 新增工具
- 在sch_mcp.py中添加新的@mcp.tool()函数
- 遵循相同的异步模式和错误处理

### 2. 参数扩展
- 修改SchedulingParameters模型
- 更新_prepare_config_for_calculation函数

### 3. 结果格式
- 修改_run_scheduling_calculation的返回格式
- 保持JSON序列化兼容性

## 部署建议

### 1. 开发环境
- 使用test_sch_mcp.py验证功能
- 使用example_agent_with_sch_mcp.py测试集成

### 2. 生产环境
- 配置适当的日志记录
- 添加性能监控
- 考虑缓存机制

### 3. 容器化
```dockerfile
FROM python:3.9
COPY requirements.txt .
RUN pip install -r requirements.txt
COPY . .
CMD ["python", "sch_mcp.py"]
```

## 最佳实践

### 1. 错误处理
- 总是返回结构化的JSON响应
- 包含success字段指示操作状态
- 提供详细的错误信息和建议

### 2. 性能优化
- 使用asyncio.to_thread处理CPU密集型计算
- 避免在主线程中执行长时间运行的操作
- 考虑结果缓存

### 3. 用户体验
- 提供清晰的工具描述
- 使用类型注解和Field描述
- 返回易于理解的结果格式

## 故障排除

### 1. 常见问题
- 模块导入失败: 检查Python路径
- 数据文件不存在: 验证文件路径
- 计算失败: 检查输入参数格式

### 2. 调试方法
- 使用test_sch_mcp.py单独测试工具
- 检查flood_control模块的可访问性
- 验证input.json配置文件

### 3. 日志分析
- MCP服务器会输出详细的执行日志
- 关注异常堆栈信息
- 检查数据验证步骤的输出
