# tavily.py

import os
from dotenv import load_dotenv
from langchain_tavily import TavilySearch
from mcp.server.fastmcp import FastMCP
from pydantic import BaseModel, Field

# 加载环境变量
load_dotenv(override=True)

# 初始化 MCP 服务器
mcp = FastMCP("TavilySearchServer")

# 检查 TAVILY_API_KEY 是否存在
if not os.getenv("TAVILY_API_KEY"):
    raise ValueError("错误：环境变量 TAVILY_API_KEY 未设置。请在 .env 文件中提供您的 Tavily API 密钥。")

# 创建 Tavily 搜索工具实例
# max_results 可以根据需要调整
search_tool = TavilySearch(max_results=5, topic="general")



@mcp.tool()
async def search(query: str) -> str:
    """
    当需要在线搜索实时信息、新闻或与数据分析无关的问题时，调用此工具。
    :param query: 搜索查询字符串。
    :return: 搜索结果列表。
    """
    print(f"正在使用 Tavily 搜索: '{query}'...")
    try:
        # Langchain 的工具通常是同步的，但在 FastMCP 的异步环境中运行
        # FastMCP 会自动处理线程转换，或者我们可以手动调用
        # TavilySearch 的 ainvoke 方法（如果可用）
        if hasattr(search_tool, 'ainvoke'):
            result = await search_tool.ainvoke({"query": query})
        else:
            result = search_tool.invoke(query)

        print("搜索完成。")
        return result
    except Exception as e:
        return f"Tavily 搜索时发生错误: {e}"


if __name__ == "__main__":
    print("Tavily 搜索 MCP 服务已启动...")
    mcp.run(transport='stdio')