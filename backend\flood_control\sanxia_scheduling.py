import pandas as pd
import numpy as np
import json
import os
from scipy.interpolate import interp1d


def load_config():
    """从parameter/input.json加载配置"""
    current_dir = os.path.dirname(os.path.abspath(__file__))
    config_path = os.path.join(current_dir, 'parameter', 'input.json')
    with open(config_path, 'r', encoding='utf-8') as f:
        config = json.load(f)
    return config


def load_capacity_curve(file_path):
    """水位库容曲线数据"""
    df = pd.read_excel(file_path)
    level_col = df.columns[0]
    capacity_col = df.columns[1]

    # 水位库容插值
    V_from_Z = interp1d(df[level_col], df[capacity_col] * 1e8, kind='linear', fill_value="extrapolate")
    Z_from_V = interp1d(df[capacity_col] * 1e8, df[level_col], kind='linear', fill_value="extrapolate")

    return V_from_Z, Z_from_V


def load_drainage_curve(file_path):
    """泄流能力曲线数据"""
    df = pd.read_excel(file_path)
    level_col = df.columns[0]
    drainage_col = df.columns[1]

    # 泄流能力插值
    drainage_from_level = interp1d(df[level_col], df[drainage_col], kind='linear', fill_value="extrapolate")

    return drainage_from_level


def load_flood_data(file_path, year, frequency, yl_boolean):
    """根据年份和频率加载洪水数据"""
    try:
        # 三峡数据
        sx_sheet_name = f"{year}三峡"
        df_sx = pd.read_excel(file_path, sheet_name=sx_sheet_name)

        # 根据频率找到对应的列
        frequency_columns = df_sx.iloc[0]
        freq_col_idx = None
        for idx, col_val in enumerate(frequency_columns):
            if str(col_val) == frequency:
                freq_col_idx = idx
                break

        if freq_col_idx is None:
            raise ValueError(f"在{sx_sheet_name}工作表中未找到频率{frequency}对应的列")

        sx_data = df_sx.iloc[1:, freq_col_idx].values

        # 宜枝区间数据
        yz_sheet_name = f"{year}宜枝"
        df_yz = pd.read_excel(file_path, sheet_name=yz_sheet_name)
        yz_frequency_columns = df_yz.iloc[0]
        yz_freq_col_idx = None
        for idx, col_val in enumerate(yz_frequency_columns):
            if str(col_val) == frequency:
                yz_freq_col_idx = idx
                break

        if yz_freq_col_idx is None:
            raise ValueError(f"在{yz_sheet_name}工作表中未找到频率{frequency}对应的列")

        yz_data = df_yz.iloc[1:, yz_freq_col_idx].values

        xjb_sheet_name = f"{year}向家坝"
        df_xjb = pd.read_excel(file_path, sheet_name=xjb_sheet_name)
        xjb_data = df_xjb.iloc[:, 0].values

        # 宜螺区间数据
        yl_data = None
        if yl_boolean:
            df_yl = pd.read_excel(file_path, sheet_name='宜螺区间')
            yl_data = df_yl.iloc[:, 0].values

        print(f"成功加载{year}年{frequency}频率的洪水数据")
        print(f"三峡数据长度: {len(sx_data)}, 宜枝数据长度: {len(yz_data)}, 向家坝数据长度: {len(xjb_data)}")

        return sx_data, yz_data, xjb_data, yl_data

    except Exception as e:
        print(f"加载{year}年{frequency}频率洪水数据时发生错误: {e}")
        raise


def calculate_single_scenario(year, frequency, config, V_from_Z, Z_from_V, drainage_from_level, input_file_path):
    """计算单个场景的调洪结果"""
    print(f"\n开始计算{year}年{frequency}频率场景...")

    # 参数
    init_water_level = config['init_water_level']
    sanxia_min_discharge = config['sanxia_min_discharge']
    upstream_max = config['upstream_max']
    xiangjiaba_min_discharge = config['xiangjiaba_min_discharge']
    jingjiang_max_level = config['jingjiang_max_level']
    jingjiang_safe_discharge = config['jingjiang_safe_discharge']
    jingjiang_max_discharge = config['jingjiang_max_discharge']
    time_length = config['time_length']
    yl_boolean = config['yl_boolean']
    if yl_boolean:
        yl_water_level = config['yl_water_level']
        yl_control_discharge = config['yl_control_discharge']

    # 加载洪水数据
    B_col, C_col, R_col, yl_col = load_flood_data(input_file_path, year, frequency, yl_boolean)

    print(f"B列前5个值: {B_col[:5]}")
    print(f"C列前5个值: {C_col[:5]}")
    print(f"R列前5个值: {R_col[:5]}")
    if yl_boolean and yl_col is not None:
        print(f"yl列前5个值: {yl_col[:5]}")

    # 过滤NaN值并统一长度
    min_length = min(len(B_col), len(C_col), len(R_col))
    B_col = B_col[:min_length]
    C_col = C_col[:min_length]
    R_col = R_col[:min_length]

    if yl_boolean and yl_col is not None:
        yl_col = yl_col[:min_length]

    valid_indices = ~(np.isnan(B_col) | np.isnan(C_col) | np.isnan(R_col))
    B_col = B_col[valid_indices]
    C_col = C_col[valid_indices]
    R_col = R_col[valid_indices]

    if yl_boolean and yl_col is not None:
        yl_col = yl_col[valid_indices]

    print(f"过滤后数据长度: {len(B_col)}")

    n_rows = len(B_col)

    D_col = np.zeros(n_rows)  # 上游预拦蓄
    E_col = np.zeros(n_rows)  # 上游动用库容
    F_col = np.zeros(n_rows)  # 上游拦蓄
    G_col = np.zeros(n_rows)  # 三峡入库
    H_col = np.zeros(n_rows)  # 三峡初水位
    I_col = np.zeros(n_rows)  # 三峡初库容
    J_col = np.zeros(n_rows)  # 枝城合成流量
    K_col = np.zeros(n_rows)  # 枝城控制流量
    K2_col = np.zeros(n_rows)  # 螺山控制流量
    L_col = np.zeros(n_rows)  # 中间值
    M_col = np.zeros(n_rows)  # 三峡下泄1
    M2_col = np.zeros(n_rows)  # 三峡下泄2
    N_col = np.zeros(n_rows)  # 三峡下泄
    O_col = np.zeros(n_rows)  # 三峡末库容
    P_col = np.zeros(n_rows)  # 三峡末水位

    for i in range(n_rows):
        j = i + 8
        if j >= n_rows:
            j = n_rows - 1

        # D列：上游预拦蓄
        if B_col[j] > 70000:
            D_col[i] = min(10000, R_col[i] - xiangjiaba_min_discharge)
        elif B_col[j] > 60000:
            D_col[i] = min(6000, R_col[i] - xiangjiaba_min_discharge)
        elif B_col[j] > 55000:
            D_col[i] = min(4000, R_col[i] - xiangjiaba_min_discharge)
        else:
            D_col[i] = 0

        # E列：上游动用库容
        if i == 0:
            E_col[i] = D_col[i] * 3600 * time_length / 100000000
        else:
            E_col[i] = D_col[i] * 3600 * time_length / 100000000 + E_col[i-1]

        # F列：上游拦蓄
        if E_col[i] <= upstream_max:
            F_col[i] = D_col[i]
        else:
            F_col[i] = 0

        # G列：三峡入库
        if i < 8:  # 前8个数据
            G_col[i] = B_col[i]
        else:
            G_col[i] = B_col[i] - F_col[i - 8] if len(F_col) > 1 else B_col[i]

        # H列：三峡初水位
        if i == 0:
            H_col[i] = init_water_level
        else:
            H_col[i] = P_col[i-1]

        # I列：三峡初库容（根据水位插值）
        I_col[i] = V_from_Z(H_col[i]) / 1e8

        # J列：枝城合成流量
        J_col[i] = G_col[i] + C_col[i]

        # K列：枝城控制流量
        if H_col[i] > jingjiang_max_level:
            if J_col[i] >= jingjiang_max_discharge:
                K_col[i] = jingjiang_max_discharge
            else:
                K_col[i] = J_col[i]
        else:
            K_col[i] = jingjiang_safe_discharge

        # L列：中间值
        L_col[i] = (J_col[i] - K_col[i]) * time_length * 3600 / 1e8

        # M列：三峡下泄1
        M_col[i] = K_col[i] - C_col[i]

        # 三峡下泄2
        if yl_boolean and yl_col is not None:
            if i + 8 < len(yl_col):
                M2_col[i] = yl_control_discharge - yl_col[i+8]
            else:
                M2_col[i] = yl_control_discharge - yl_col[-1]  # 使用最后一个值

        # N列：三峡下泄
        if L_col[i] > 0:
            if yl_boolean:
                if H_col[i] < yl_water_level:
                    calculated_discharge = min(M_col[i], M2_col[i])
                else:
                    calculated_discharge = M_col[i]
            else:
                calculated_discharge = M_col[i]
        else:
            if H_col[i] > init_water_level:
                if i > 0:
                    initial_storage = V_from_Z(init_water_level) / 1e8  # 初始库容
                    if yl_boolean:
                        if H_col[i] < yl_water_level:
                            calculated_discharge = min(M_col[i], M2_col[i], (O_col[i-1] - initial_storage) * 1e8 / time_length / 3600 + G_col[i])
                        else:
                            calculated_discharge = min(M_col[i],
                                                       (O_col[i - 1] - initial_storage) * 1e8 / time_length / 3600 +
                                                       G_col[i])
                    else:
                        calculated_discharge = min(M_col[i], (O_col[i-1] - initial_storage) * 1e8 / time_length / 3600 + G_col[i])
                else:
                    calculated_discharge = G_col[i]
            else:
                calculated_discharge = G_col[i]

        # 根据H_col的三峡初水位和泄流能力曲线限制下泄量
        max_drainage_capacity = drainage_from_level(H_col[i])
        N_col[i] = min(calculated_discharge, max_drainage_capacity)

        # O列：三峡末库容
        O_col[i] = I_col[i] + (G_col[i] - N_col[i]) * 3600 * time_length / 100000000

        # P列：三峡末水位（根据库容插值，与初始水位取最大值）
        P_col[i] = max(Z_from_V(O_col[i] * 1e8), init_water_level)

        # 1. 如果yl_boolean为true且H_col[i]<158且P_col[i]>158，则进行试算
        if yl_boolean and H_col[i] < 158 and P_col[i] > 158:
            # 保存试算前的结果
            original_P = P_col[i]
            original_O = O_col[i]
            original_N = N_col[i]

            # 试算：把yl_boolean设置为false，重新计算N_col[i]
            # 重新计算三峡下泄（不考虑宜螺区间）
            if L_col[i] > 0:
                calculated_discharge_test = M_col[i]
            else:
                if H_col[i] > init_water_level:
                    if i > 0:
                        initial_storage = V_from_Z(init_water_level) / 1e8  # 初始库容
                        calculated_discharge_test = min(M_col[i], (O_col[i-1] - initial_storage) * 1e8 / time_length / 3600 + G_col[i])
                    else:
                        calculated_discharge_test = G_col[i]
                else:
                    calculated_discharge_test = G_col[i]

            max_drainage_capacity_test = drainage_from_level(H_col[i])
            N_test = min(calculated_discharge_test, max_drainage_capacity_test)

            # 重新计算末库容和末水位
            O_test = I_col[i] + (G_col[i] - N_test) * 3600 * time_length / 100000000
            P_test = max(Z_from_V(O_test * 1e8), init_water_level)

            # 判断试算结果
            if P_test > 158:
                # 试算后P_col[i]>158，恢复试算前的结果
                P_col[i] = original_P
                O_col[i] = original_O
                N_col[i] = original_N
            else:
                # 试算后P_col[i]<158，把P_col[i]直接修改为158，并反推末库容和下泄流量
                P_col[i] = 158
                O_col[i] = V_from_Z(158) / 1e8  # 根据水位库容曲线反推末库容
                # 根据水量平衡反推下泄流量：O = I + (G - N) * dt / 1e8
                # N = G - (O - I) * 1e8 / dt
                N_col[i] = G_col[i] - (O_col[i] - I_col[i]) * 1e8 / (3600 * time_length)

        # 2. 如果H_col[i]<171且P_col[i]>171
        if H_col[i] < 171 and P_col[i] > 171:
            P_col[i] = 171
            O_col[i] = V_from_Z(171) / 1e8  # 根据水位库容曲线反推末库容
            # 根据水量平衡反推下泄流量
            N_col[i] = G_col[i] - (O_col[i] - I_col[i]) * 1e8 / (3600 * time_length)

    A12_max_level = float(np.max(P_col[1:]) if len(P_col) > 1 else np.max(P_col))  # 三峡最高水位
    A15_upstream_use = min(float(np.max(E_col)), upstream_max)  # 上游动用
    A22_upstream_reserve = float(np.max(O_col[1:]) - 353.8 if len(O_col) > 1 else np.max(O_col) - 353.8)  # 需上游水库预留库容

    print("\n" + "=" * 50)
    print(f"计算结果汇总 - {year}年{frequency}频率")
    print("=" * 50)
    print(f"初始水位: {init_water_level:.2f} m")
    print(f"上游最大动用: {upstream_max:.2f}")
    print(f"荆江特大洪水水位: {jingjiang_max_level:.2f} m")
    print(f"三峡最高水位: {A12_max_level:.2f} m")
    print(f"上游动用: {A15_upstream_use:.2f}")
    print(f"荆江安全泄量: {jingjiang_safe_discharge}")
    print(f"荆江最大下泄: {jingjiang_max_discharge}")
    print(f"三峡最小下泄: {sanxia_min_discharge}")
    print(f"需上游水库预留库容: {A22_upstream_reserve:.2f}")
    print(f"向家坝最小下泄: {xiangjiaba_min_discharge}")
    print(f"计算时长: {time_length}")

    # 创建结果数据框
    summary_col = [None for _ in range(n_rows)]

    if n_rows > 0:
        summary_col[0] = "初始水位"
    if n_rows > 1:
        summary_col[1] = init_water_level
    if n_rows > 3:
        summary_col[3] = "上游最大动用"
    if n_rows > 4:
        summary_col[4] = upstream_max
    if n_rows > 6:
        summary_col[6] = "荆江特大洪水水位"
    if n_rows > 7:
        summary_col[7] = jingjiang_max_level
    if n_rows > 10:
        summary_col[10] = "三峡最高水位"
    if n_rows > 11:
        summary_col[11] = round(A12_max_level, 2)
    if n_rows > 13:
        summary_col[13] = "上游动用"
    if n_rows > 14:
        summary_col[14] = round(A15_upstream_use, 2)
    if n_rows > 16:
        summary_col[16] = jingjiang_safe_discharge
    if n_rows > 17:
        summary_col[17] = jingjiang_max_discharge
    if n_rows > 18:
        summary_col[18] = sanxia_min_discharge
    if n_rows > 20:
        summary_col[20] = "需上游水库预留库容"
    if n_rows > 21:
        summary_col[21] = round(A22_upstream_reserve, 2)
    if n_rows > 23:
        summary_col[23] = "向家坝最小下泄"
    if n_rows > 24:
        summary_col[24] = xiangjiaba_min_discharge
    if n_rows > 26:
        summary_col[26] = "计算时长"
    if n_rows > 27:
        summary_col[27] = time_length

    result_df = pd.DataFrame({
        '汇总信息': summary_col,
        '三峡原始入库': B_col,
        # '区间': C_col,
        # '预': D_col,
        # '动用': E_col,
        '上游拦蓄': F_col,
        '三峡入库': G_col,
        '三峡初水位': H_col.round(2),
        '三峡初库容': I_col.round(2),
        # '三峡下泄1': M_col,
        # '三峡下泄2': M2_col,
        '三峡下泄': N_col,
        '三峡末库容': O_col.round(2),
        '三峡末水位': P_col.round(2),
        # '向家坝': R_col
    })

    return result_df, f"{year}年{frequency}频率"

def perform_flood_routing():
    """执行多场景三峡调洪计算"""
    print("开始执行三峡调洪计算...")
    try:
        config = load_config()
        print("配置加载成功")
    except Exception as e:
        print(f"配置加载失败: {e}")
        return

    # 获取场景列表
    scenarios = config.get('scenarios', [])
    if not scenarios:
        print("错误: 配置文件中未找到scenarios参数")
        return

    print(f"共有 {len(scenarios)} 个计算场景")

    current_dir = os.path.dirname(os.path.abspath(__file__))
    input_file_path = os.path.join(current_dir, config['data_path']['flood_file_path'])
    capacity_file_path = os.path.join(current_dir, config['data_path']['capacity_file_path'])
    drainage_file_path = os.path.join(current_dir, config['data_path']['drainage_file_path'])
    output_file_path = os.path.join(current_dir, config['data_path']['output_file_path'])

    # 加载水位库容曲线和泄流能力曲线
    try:
        V_from_Z, Z_from_V = load_capacity_curve(capacity_file_path)
        drainage_from_level = load_drainage_curve(drainage_file_path)
        print("成功加载水位库容曲线和泄流能力曲线")
    except Exception as e:
        print(f"加载曲线数据失败: {e}")
        return

    # 存储所有场景的计算结果
    all_results = {}
    # 循环计算每个场景
    for i, scenario in enumerate(scenarios, 1):
        year = scenario['year']
        frequency = scenario['frequency']

        print(f"\n{'='*60}")
        print(f"正在计算第 {i}/{len(scenarios)} 个场景: {year}年 {frequency}频率")
        print(f"{'='*60}")

        try:
            result_df, sheet_name = calculate_single_scenario(
                year, frequency, config, V_from_Z, Z_from_V,
                drainage_from_level, input_file_path
            )
            all_results[sheet_name] = result_df
            print(f"场景 {sheet_name} 计算完成")

        except Exception as e:
            print(f"计算场景 {year}年{frequency}频率 时发生错误: {e}")
            continue

    # 将所有结果写入Excel文件的不同工作表
    if all_results:
        print(f"\n{'='*60}")
        print(f"正在将所有计算结果写入Excel文件: {output_file_path}")
        print(f"{'='*60}")

        try:
            with pd.ExcelWriter(output_file_path, engine='openpyxl') as writer:
                for sheet_name, result_df in all_results.items():
                    result_df.to_excel(writer, sheet_name=sheet_name, index=False)
                    print(f"已写入工作表: {sheet_name}")

            print(f"\n成功将所有结果写入到 '{output_file_path}'")
            print(f"共包含 {len(all_results)} 个工作表")

        except Exception as e:
            print(f"写入Excel文件时发生错误: {e}")
    else:
        print("没有成功计算的结果可以写入")

    print("\n所有计算完成！")


if __name__ == '__main__':
    perform_flood_routing()