import { v4 as uuidv4 } from "uuid";
import { ReactNode, useEffect, useRef } from "react";
import { motion } from "framer-motion";
import { cn } from "@/lib/utils";
import { useStreamContext } from "@/providers/Stream";
import { useState, FormEvent } from "react";
import { Button } from "../ui/button";
import { Checkpoint, Message } from "@langchain/langgraph-sdk";
import { AssistantMessage, AssistantMessageLoading } from "./messages/ai";
import { HumanMessage } from "./messages/human";
import {
  DO_NOT_RENDER_ID_PREFIX,
  ensureToolCallsHaveResponses,
} from "@/lib/ensure-tool-responses";
import { LangGraphLogoSVG } from "../icons/langgraph";
import { TooltipIconButton } from "./tooltip-icon-button";
import {
  ArrowDown,
  LoaderCircle,
  PanelRightOpen,
  PanelRightClose,
  SquarePen,
  XIcon,
  Plus,
  CircleX,
  FileText,
} from "lucide-react";
import { useQueryState, parseAsBoolean } from "nuqs";
import { StickToBottom, useStickToBottomContext } from "use-stick-to-bottom";
import ThreadHistory from "./history";
import { toast } from "sonner";
import { useMediaQuery } from "@/hooks/useMediaQuery";
import { Label } from "../ui/label";
import { Switch } from "../ui/switch";
import { GitHubSVG } from "../icons/github";
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from "../ui/tooltip";
import { useFileUpload } from "@/hooks/use-file-upload";
import { ContentBlocksPreview } from "./ContentBlocksPreview";
import { getContentString } from "./utils";
import {
  useArtifactOpen,
  ArtifactContent,
  ArtifactTitle,
  useArtifactContext,
} from "./artifact";
import WordPreview from "@/components/word-preview";

function tryParseJsonFromText(text: string): Record<string, any> | null {
  try {
    const obj = JSON.parse(text);
    if (obj && typeof obj === 'object') return obj;
  } catch {}
  try {
    const match = text.match(/```json[\s\S]*?```/i);
    if (match) {
      const inner = match[0].replace(/```json/i, '').replace(/```$/, '');
      const obj = JSON.parse(inner.trim());
      if (obj && typeof obj === 'object') return obj;
    }
  } catch {}
  try {
    const first = text.indexOf('{');
    const last = text.lastIndexOf('}');
    if (first >= 0 && last > first) {
      const inner = text.slice(first, last + 1);
      const obj = JSON.parse(inner);
      if (obj && typeof obj === 'object') return obj;
    }
  } catch {}
  return null;
}

function StickyToBottomContent(props: {
  content: ReactNode;
  footer?: ReactNode;
  className?: string;
  contentClassName?: string;
}) {
  const context = useStickToBottomContext();
  return (
    <div
      ref={context.scrollRef}
      style={{ width: "100%", height: "100%" }}
      className={props.className}
    >
      <div
        ref={context.contentRef}
        className={props.contentClassName}
      >
        {props.content}
      </div>

      {props.footer}
    </div>
  );
}

function ScrollToBottom(props: { className?: string }) {
  const { isAtBottom, scrollToBottom } = useStickToBottomContext();

  if (isAtBottom) return null;
  return (
    <Button
      variant="outline"
      className={props.className}
      onClick={() => scrollToBottom()}
    >
      <ArrowDown className="h-4 w-4" />
      <span>Scroll to bottom</span>
    </Button>
  );
}

function OpenGitHubRepo() {
  return (
    <TooltipProvider>
      <Tooltip>
        <TooltipTrigger asChild>
          <a
            href="https://github.com/langchain-ai/agent-chat-ui"
            target="_blank"
            className="flex items-center justify-center"
          >
            <GitHubSVG
              width="24"
              height="24"
            />
          </a>
        </TooltipTrigger>
        <TooltipContent side="left">
          <p>Open GitHub repo</p>
        </TooltipContent>
      </Tooltip>
    </TooltipProvider>
  );
}

export function Thread() {
  const [artifactContext, setArtifactContext] = useArtifactContext();
  const [artifactOpen, closeArtifact] = useArtifactOpen();

  const [threadId, _setThreadId] = useQueryState("threadId");
  const [chatHistoryOpen, setChatHistoryOpen] = useQueryState(
    "chatHistoryOpen",
    parseAsBoolean.withDefault(false),
  );
  const [hideToolCalls, setHideToolCalls] = useQueryState(
    "hideToolCalls",
    parseAsBoolean.withDefault(false),
  );
  const [wordPreviewOpen, setWordPreviewOpen] = useQueryState(
    "wordPreviewOpen",
    parseAsBoolean.withDefault(true),
  );
  const [input, setInput] = useState("");
  const {
    contentBlocks,
    setContentBlocks,
    handleFileUpload,
    dropRef,
    removeBlock,
    resetBlocks,
    dragOver,
    handlePaste,
  } = useFileUpload();
  const [firstTokenReceived, setFirstTokenReceived] = useState(false);
  const isLargeScreen = useMediaQuery("(min-width: 1024px)");

  const stream = useStreamContext();
  const messages = stream.messages;
  const isLoading = stream.isLoading;

  const lastError = useRef<string | undefined>(undefined);

  const setThreadId = (id: string | null) => {
    _setThreadId(id);

    // close artifact and reset artifact context
    closeArtifact();
    setArtifactContext({});
  };

  useEffect(() => {
    if (!stream.error) {
      lastError.current = undefined;
      return;
    }
    try {
      const message = (stream.error as any).message;
      if (!message || lastError.current === message) {
        // Message has already been logged. do not modify ref, return early.
        return;
      }

      // Message is defined, and it has not been logged yet. Save it, and send the error
      lastError.current = message;
      toast.error("An error occurred. Please try again.", {
        description: (
          <p>
            <strong>Error:</strong> <code>{message}</code>
          </p>
        ),
        richColors: true,
        closeButton: true,
      });
    } catch {
      // no-op
    }
  }, [stream.error]);

  // TODO: this should be part of the useStream hook
  const prevMessageLength = useRef(0);
  useEffect(() => {
    if (
      messages.length !== prevMessageLength.current &&
      messages?.length &&
      messages[messages.length - 1].type === "ai"
    ) {
      setFirstTokenReceived(true);
    }

    prevMessageLength.current = messages.length;
  }, [messages]);

  // 解析 AI 返回的章节更新指令，并应用到右侧文档；否则流式结束时刷新一次
  const processedAiIds = useRef<Set<string>>(new Set());
  useEffect(() => {
    if (!messages.length) return;
    const last = messages[messages.length - 1];
    if (last.type !== "ai" || !last.id) return;
    if (isLoading) return;
    if (processedAiIds.current.has(last.id)) return;

    const contentString = require('../thread/utils').getContentString(last.content ?? []);
    const obj = tryParseJsonFromText(contentString);
    const isWordUpdate = !!obj && typeof obj === 'object' && (
      (obj as any).action === 'update_word_section' ||
      (obj as any).action === 'replace_section' ||
      (obj as any).action === 'update_section_html'
    );

    const applyUpdate = async () => {
      try {
        const filePath = typeof window !== 'undefined' ? window.localStorage.getItem('word:currentFilePath') || '' : '';
        const currentSectionId = typeof window !== 'undefined' ? window.localStorage.getItem('word:currentSectionId') || '' : '';
        const sectionId = (obj as any).section_id || currentSectionId;
        let html = (obj as any).html || (obj as any).content_html || (obj as any).content || '';
        if (!filePath || !sectionId || !html) return;

        // 若存在选区，则仅替换选中的片段，并在此基础上合并图片，避免丢失
        try {
          const baseHtmlRaw = window.localStorage.getItem('word:currentSectionHtml') || '';
          const selectedHtml = window.localStorage.getItem('word:selectedHtml') || '';
          const selectedText = window.localStorage.getItem('word:selectedText') || '';

          const replaceSelection = (originalHtml: string, selectionHtml: string, selectionText: string, replacementHtml: string) => {
            let working = originalHtml;
            if (selectionHtml && working.includes(selectionHtml)) {
              return working.replace(selectionHtml, replacementHtml);
            }
            if (selectionText) {
              // 使用一个保守的替换：仅替换首次出现的文本
              const escaped = selectionText.replace(/[.*+?^${}()|[\]\\]/g, '\\$&');
              const re = new RegExp(escaped);
              return working.replace(re, replacementHtml);
            }
            return replacementHtml || originalHtml;
          };

          let baseHtml = baseHtmlRaw;
          if ((selectedHtml || selectedText) && baseHtml) {
            // 先在原HTML中用AI返回的html替换选区
            baseHtml = replaceSelection(baseHtmlRaw, selectedHtml, selectedText, html);
            html = baseHtml;
          }

          const mergeImages = (originalHtml: string, newHtml: string) => {
            if (!originalHtml) return newHtml;
            const parser = new DOMParser();
            const baseDoc = parser.parseFromString(originalHtml, 'text/html');
            const newDoc = parser.parseFromString(newHtml, 'text/html');
            // 移除选区高亮标记
            baseDoc.querySelectorAll('[data-word-selection]')?.forEach((n) => n.replaceWith(...Array.from(n.childNodes)));
            newDoc.querySelectorAll('[data-word-selection]')?.forEach((n) => n.replaceWith(...Array.from(n.childNodes)));
            const baseImgs = Array.from(baseDoc.querySelectorAll('img')) as HTMLImageElement[];
            const newImgs = Array.from(newDoc.querySelectorAll('img')) as HTMLImageElement[];
            const newSrcs = new Set(newImgs.map((i) => i.getAttribute('src') || ''));
            if (baseImgs.length && newImgs.length === 0) {
              const container = newDoc.body || newDoc;
              baseImgs.forEach((img) => {
                const clone = img.cloneNode(true) as HTMLImageElement;
                container.appendChild(clone);
              });
            } else if (baseImgs.length) {
              // 确保缺失的图片被追加
              const container = newDoc.body || newDoc;
              baseImgs.forEach((img) => {
                const src = img.getAttribute('src') || '';
                if (!newSrcs.has(src) && src) {
                  container.appendChild(img.cloneNode(true));
                }
              });
            }
            return newDoc.body ? newDoc.body.innerHTML : newHtml;
          };
          html = mergeImages(baseHtml, html);
        } catch {}
        const res = await fetch('/api/word/update-section', {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify({ file_path: filePath, section_id: sectionId, new_html: html }),
        });
        if (!res.ok) return;
        const data = await res.json();
        if (data && data.success) {
          try {
            window.localStorage.setItem('word:currentSectionHtml', html);
          } catch {}
          window.dispatchEvent(new Event('word-doc-refresh'));
        }
      } catch (e) {
        console.error('Failed to apply word update:', e);
      }
    };

    processedAiIds.current.add(last.id);
    if (isWordUpdate) {
      applyUpdate();
    } else {
      window.dispatchEvent(new Event('word-doc-refresh'));
    }
  }, [messages, isLoading]);

  const handleSubmit = (e: FormEvent) => {
    e.preventDefault();
    if ((input.trim().length === 0 && contentBlocks.length === 0) || isLoading)
      return;
    setFirstTokenReceived(false);

    // 从本地存储读取当前文档/章节上下文；如果存在选区，则优先使用选区
    const docName = typeof window !== 'undefined' ? window.localStorage.getItem('word:docName') || '' : '';
    const filePath = typeof window !== 'undefined' ? window.localStorage.getItem('word:currentFilePath') || '' : '';
    const sectionId = typeof window !== 'undefined' ? window.localStorage.getItem('word:currentSectionId') || '' : '';
    const sectionTitle = typeof window !== 'undefined' ? window.localStorage.getItem('word:currentSectionTitle') || '' : '';
    const sectionContent = typeof window !== 'undefined' ? window.localStorage.getItem('word:currentSectionContent') || '' : '';
    const selectedText = typeof window !== 'undefined' ? window.localStorage.getItem('word:selectedText') || '' : '';
    const selectedHtml = typeof window !== 'undefined' ? window.localStorage.getItem('word:selectedHtml') || '' : '';
    const hasSelection = !!selectedText && selectedText.trim().length > 0;
    const hasWordContext = !!filePath && !!sectionId && (!!sectionContent || hasSelection);

    // 构造仅用于模型的隐藏上下文消息（在UI中不渲染）
    const contextParts: { type: 'text'; text: string }[] = [];
    if (hasWordContext) {
      if (sectionContent) {
        contextParts.push({
          type: 'text',
          text: `【上下文-当前章节】文档: ${docName || '未命名'}，路径: ${filePath}；章节: ${sectionTitle || sectionId}（${sectionId}）。以下是该章节完整内容：\n\n${sectionContent}`,
        });
      }
      if (hasSelection) {
        contextParts.push({
          type: 'text',
          text: `【重要说明】用户在上述章节中选中了一个片段用于定向操作。请严格仅对该选中片段进行修改，不要改动章节中未被选中的其他内容。`,
        });
        contextParts.push({
          type: 'text',
          text: `【上下文-所选片段】如下（纯文本）：\n\n${selectedText}`,
        });
        if (selectedHtml) {
          contextParts.push({
            type: 'text',
            text: `【上下文-所选片段HTML】如下：\n\n${selectedHtml}`,
          });
        }
      }
    }

    const hiddenContextMessage: Message | null = contextParts.length
      ? {
          id: `${DO_NOT_RENDER_ID_PREFIX}${uuidv4()}`,
          type: 'human',
          content: contextParts,
        }
      : null;

    // 可见的人类消息（仅用户输入与附件）
    const newHumanMessage: Message = {
      id: uuidv4(),
      type: "human",
      content: [
        ...(input.trim().length > 0 ? [{ type: "text", text: input }] : []),
        ...contentBlocks,
      ] as Message["content"],
    };

    const toolMessages = ensureToolCallsHaveResponses(stream.messages);

    const context =
      Object.keys(artifactContext).length > 0 ? artifactContext : undefined;

    stream.submit(
      { messages: [...toolMessages, ...(hiddenContextMessage ? [hiddenContextMessage] : []), newHumanMessage], context },
      {
        streamMode: ["values"],
        optimisticValues: (prev) => ({
          ...prev,
          context,
          messages: [
            ...(prev.messages ?? []),
            ...toolMessages,
            // 不把隐藏上下文消息放到本地UI渲染
            newHumanMessage,
          ],
        }),
      },
    );

    setInput("");
    setContentBlocks([]);
  };

  const handleRegenerate = (
    parentCheckpoint: Checkpoint | null | undefined,
  ) => {
    // Do this so the loading state is correct
    prevMessageLength.current = prevMessageLength.current - 1;
    setFirstTokenReceived(false);
    stream.submit(undefined, {
      checkpoint: parentCheckpoint,
      streamMode: ["values"],
    });
  };

  const chatStarted = !!threadId || !!messages.length;
  const hasNoAIOrToolMessages = !messages.find(
    (m) => m.type === "ai" || m.type === "tool",
  );

  return (
    <div className="flex h-screen w-full overflow-hidden">
      <div className="relative hidden lg:flex">
        <motion.div
          className="absolute z-20 h-full overflow-hidden border-r bg-white"
          style={{ width: 300 }}
          animate={
            isLargeScreen
              ? { x: chatHistoryOpen ? 0 : -300 }
              : { x: chatHistoryOpen ? 0 : -300 }
          }
          initial={{ x: -300 }}
          transition={
            isLargeScreen
              ? { type: "spring", stiffness: 300, damping: 30 }
              : { duration: 0 }
          }
        >
          <div
            className="relative h-full"
            style={{ width: 300 }}
          >
            <ThreadHistory />
          </div>
        </motion.div>
      </div>

      <div
        className={cn(
          // 左小右大：右侧更聚焦 Word，提升可读性
          "grid w-full grid-cols-[1.2fr_0fr] transition-all duration-500 min-h-0",
          (artifactOpen || wordPreviewOpen) && "grid-cols-[1fr_2fr] lg:grid-cols-[1.1fr_2.2fr]",
        )}
      >
        <motion.div
          className={cn(
            "relative flex min-w-0 flex-1 flex-col overflow-hidden",
            !chatStarted && "grid-rows-[1fr]",
          )}
          layout={isLargeScreen}
          animate={{
            marginLeft: chatHistoryOpen ? (isLargeScreen ? 300 : 0) : 0,
            width: chatHistoryOpen
              ? isLargeScreen
                ? "calc(100% - 300px)"
                : "100%"
              : "100%",
          }}
          transition={
            isLargeScreen
              ? { type: "spring", stiffness: 300, damping: 30 }
              : { duration: 0 }
          }
        >
          {!chatStarted && (
            <div className="absolute top-0 left-0 z-10 flex w-full items-center justify-between gap-3 p-2 pl-4">
              <div>
                {(!chatHistoryOpen || !isLargeScreen) && (
                  <Button
                    className="hover:bg-gray-100"
                    variant="ghost"
                    onClick={() => setChatHistoryOpen((p) => !p)}
                  >
                    {chatHistoryOpen ? (
                      <PanelRightOpen className="size-5" />
                    ) : (
                      <PanelRightClose className="size-5" />
                    )}
                  </Button>
                )}
              </div>
              <div className="absolute top-2 right-4 flex items-center">
                <OpenGitHubRepo />
              </div>
            </div>
          )}
          {chatStarted && (
            <div className="relative z-10 flex items-center justify-between gap-3 p-2">
              <div className="relative flex items-center justify-start gap-2">
                <div className="absolute left-0 z-10">
                  {(!chatHistoryOpen || !isLargeScreen) && (
                    <Button
                      className="hover:bg-gray-100"
                      variant="ghost"
                      onClick={() => setChatHistoryOpen((p) => !p)}
                    >
                      {chatHistoryOpen ? (
                        <PanelRightOpen className="size-5" />
                      ) : (
                        <PanelRightClose className="size-5" />
                      )}
                    </Button>
                  )}
                </div>
                <motion.button
                  className="flex cursor-pointer items-center gap-2"
                  onClick={() => setThreadId(null)}
                  animate={{
                    marginLeft: !chatHistoryOpen ? 48 : 0,
                  }}
                  transition={{
                    type: "spring",
                    stiffness: 300,
                    damping: 30,
                  }}
                >
                  <LangGraphLogoSVG
                    width={32}
                    height={32}
                  />
                  <span className="text-xl font-semibold tracking-tight">
                    agent
                  </span>
                </motion.button>
              </div>

              <div className="flex items-center gap-4">
                <div className="flex items-center">
                  <OpenGitHubRepo />
                </div>
                <TooltipIconButton
                  size="lg"
                  className="p-4"
                  tooltip="Word文档预览"
                  variant="ghost"
                  onClick={() => setWordPreviewOpen((p) => !p)}
                >
                  <FileText className="size-5" />
                </TooltipIconButton>
                <TooltipIconButton
                  size="lg"
                  className="p-4"
                  tooltip="New thread"
                  variant="ghost"
                  onClick={() => setThreadId(null)}
                >
                  <SquarePen className="size-5" />
                </TooltipIconButton>
              </div>

              <div className="from-background to-background/0 absolute inset-x-0 top-full h-5 bg-gradient-to-b" />
            </div>
          )}

          <StickToBottom className="relative flex-1 overflow-hidden bg-gradient-to-b from-white to-slate-50">
            <StickyToBottomContent
              className={cn(
                "absolute inset-0 overflow-y-scroll px-4 [&::-webkit-scrollbar]:w-1.5 [&::-webkit-scrollbar-thumb]:rounded-full [&::-webkit-scrollbar-thumb]:bg-gray-300 [&::-webkit-scrollbar-track]:bg-transparent",
                !chatStarted && "mt-[25vh] flex flex-col items-stretch",
                chatStarted && "grid grid-rows-[1fr_auto]",
              )}
              contentClassName="pt-8 pb-16  max-w-3xl mx-auto flex flex-col gap-4 w-full"
              content={
                <>
                  {messages
                    .filter((m) => !m.id?.startsWith(DO_NOT_RENDER_ID_PREFIX))
                    .map((message, index) =>
                      message.type === "human" ? (
                        <HumanMessage
                          key={message.id || `${message.type}-${index}`}
                          message={message}
                          isLoading={isLoading}
                        />
                      ) : (
                        <AssistantMessage
                          key={message.id || `${message.type}-${index}`}
                          message={message}
                          isLoading={isLoading}
                          handleRegenerate={handleRegenerate}
                        />
                      ),
                    )}
                  {/* Special rendering case where there are no AI/tool messages, but there is an interrupt.
                    We need to render it outside of the messages list, since there are no messages to render */}
                  {hasNoAIOrToolMessages && !!stream.interrupt && (
                    <AssistantMessage
                      key="interrupt-msg"
                      message={undefined}
                      isLoading={isLoading}
                      handleRegenerate={handleRegenerate}
                    />
                  )}
                  {isLoading && !firstTokenReceived && (
                    <AssistantMessageLoading />
                  )}
                </>
              }
              footer={
                <div className="sticky bottom-0 flex flex-col items-center gap-8 bg-white">
                  {!chatStarted && (
                    <div className="flex items-center gap-3">
                      <LangGraphLogoSVG className="h-8 flex-shrink-0" />
                      <h1 className="text-2xl font-semibold tracking-tight">
                        agent
                      </h1>
                    </div>
                  )}

                  <ScrollToBottom className="animate-in fade-in-0 zoom-in-95 absolute bottom-full left-1/2 mb-4 -translate-x-1/2" />

                  <div
                    ref={dropRef}
                    className={cn(
                      "bg-muted relative z-10 mx-auto mb-8 w-full max-w-3xl rounded-2xl shadow-xs transition-all",
                      dragOver
                        ? "border-primary border-2 border-dotted"
                        : "border border-solid",
                    )}
                  >
                    <form
                      onSubmit={handleSubmit}
                      className="mx-auto grid max-w-3xl grid-rows-[1fr_auto] gap-2"
                    >
                      <ContentBlocksPreview
                        blocks={contentBlocks}
                        onRemove={removeBlock}
                      />
                      <textarea
                        value={input}
                        onChange={(e) => setInput(e.target.value)}
                        onPaste={handlePaste}
                        onKeyDown={(e) => {
                          if (
                            e.key === "Enter" &&
                            !e.shiftKey &&
                            !e.metaKey &&
                            !e.nativeEvent.isComposing
                          ) {
                            e.preventDefault();
                            const el = e.target as HTMLElement | undefined;
                            const form = el?.closest("form");
                            form?.requestSubmit();
                          }
                        }}
                        placeholder="Type your message..."
                        className="field-sizing-content resize-none border-none bg-transparent p-3.5 pb-0 shadow-none ring-0 outline-none focus:ring-0 focus:outline-none"
                      />

                      <div className="flex items-center gap-6 p-2 pt-4">
                        <div>
                          <div className="flex items-center space-x-2">
                            <Switch
                              id="render-tool-calls"
                              checked={hideToolCalls ?? false}
                              onCheckedChange={setHideToolCalls}
                            />
                            <Label
                              htmlFor="render-tool-calls"
                              className="text-sm text-gray-600"
                            >
                              Hide Tool Calls
                            </Label>
                          </div>
                        </div>
                        <Label
                          htmlFor="file-input"
                          className="flex cursor-pointer items-center gap-2"
                        >
                          <Plus className="size-5 text-gray-600" />
                          <span className="text-sm text-gray-600">
                            Upload PDF or Image
                          </span>
                        </Label>
                        <input
                          id="file-input"
                          type="file"
                          onChange={handleFileUpload}
                          multiple
                          accept="image/jpeg,image/png,image/gif,image/webp,application/pdf"
                          className="hidden"
                        />
                        {stream.isLoading ? (
                          <Button
                            key="stop"
                            onClick={() => stream.stop()}
                            className="ml-auto"
                          >
                            <LoaderCircle className="h-4 w-4 animate-spin" />
                            Cancel
                          </Button>
                        ) : (
                          <Button
                            type="submit"
                            className="ml-auto shadow-md transition-all"
                            disabled={
                              isLoading ||
                              (!input.trim() && contentBlocks.length === 0)
                            }
                          >
                            Send
                          </Button>
                        )}
                      </div>
                    </form>
                  </div>
                </div>
              }
            />
          </StickToBottom>
        </motion.div>
        <div className="relative flex flex-col border-l min-h-0">
          <div className="absolute inset-0 flex min-w-[30vw] flex-col overflow-hidden min-h-0">
            {artifactOpen ? (
              <>
                 <div className="grid grid-cols-[1fr_auto] border-b p-4 bg-white/70 backdrop-blur supports-[backdrop-filter]:bg-white/60">
                  <ArtifactTitle className="truncate overflow-hidden" />
                  <button
                    onClick={closeArtifact}
                    className="cursor-pointer"
                  >
                    <XIcon className="size-5" />
                  </button>
                </div>
                <ArtifactContent className="relative flex-grow" />
              </>
            ) : wordPreviewOpen ? (
              <>
                <WordPreview className="relative flex-grow min-h-0" />
              </>
            ) : null}
          </div>
        </div>
      </div>
    </div>
  );
}
