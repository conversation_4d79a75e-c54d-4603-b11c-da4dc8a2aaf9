#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
通用数据导入配置文件
请根据你的MySQL配置和Excel文件路径修改以下参数
"""

# MySQL数据库配置
DB_CONFIG = {
    'host': 'localhost',        # MySQL服务器地址
    'port': 3306,              # MySQL端口号
    'database': 'agent',       # 数据库名称
    'user': 'root',            # 用户名，请根据实际情况修改
    'password': '1234',        # 密码，请根据实际情况修改
    'charset': 'utf8mb4',      # 字符集，支持中文
    'autocommit': False,       # 自动提交
    'use_unicode': True        # 使用Unicode
}

# Excel文件路径配置
# 用户可以在这里配置要导入的Excel文件路径
# EXCEL_FILE_PATH = '../water-data.xlsx'  # 默认路径，用户可以修改
EXCEL_FILE_PATH = r'G:\Study\Python\Workflow\flood_control\parameter\大洪水\mysql.xlsx'  # 默认路径，用户可以修改

# 数据导入配置
IMPORT_CONFIG = {
    'batch_size': 1000,        # 批量插入的数据条数
    'max_varchar_length': 255, # VARCHAR字段的最大长度
    'datetime_columns': ['date', 'time', 'datetime', '时间', '日期'],  # 可能的时间列名
    'skip_empty_sheets': True, # 是否跳过空的Sheet
    'create_index_on_datetime': True,  # 是否在时间字段上创建索引
}

# 日志配置
LOG_CONFIG = {
    'level': 'INFO',
    'format': '%(asctime)s - %(levelname)s - %(message)s',
    'file': None,  # 如果需要输出到文件，设置文件路径
}
