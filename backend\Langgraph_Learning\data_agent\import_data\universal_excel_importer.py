#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
通用Excel数据导入MySQL程序
支持多Sheet处理、中文转拼音、数据类型自动识别、智能数据更新等功能
"""

import pandas as pd
import mysql.connector
from mysql.connector import Error
import sys
import os
from datetime import datetime
import logging
from typing import Dict, List, Optional, Tuple, Any
from pathlib import Path

# 导入配置和工具模块
from db_config import DB_CONFIG, EXCEL_FILE_PATH, IMPORT_CONFIG, LOG_CONFIG
from utils import (
    sanitize_table_name,
    detect_column_type,
    create_table_sql,
    sanitize_column_name,
    get_datetime_column,
    prepare_data_for_insert,
    get_database_column_names
)

# 配置日志
logging.basicConfig(
    level=getattr(logging, LOG_CONFIG['level']),
    format=LOG_CONFIG['format'],
    filename=LOG_CONFIG['file']
)
logger = logging.getLogger(__name__)

class UniversalExcelImporter:
    """通用Excel导入器"""
    
    def __init__(self, excel_path: str = None, db_config: Dict = None):
        """
        初始化导入器
        
        Args:
            excel_path: Excel文件路径
            db_config: 数据库配置
        """
        self.excel_path = excel_path or EXCEL_FILE_PATH
        self.db_config = db_config or DB_CONFIG
        self.connection = None
        
    def create_connection(self) -> bool:
        """创建MySQL数据库连接"""
        try:
            self.connection = mysql.connector.connect(**self.db_config)
            if self.connection.is_connected():
                logger.info("成功连接到MySQL数据库")
                return True
        except Error as e:
            logger.error(f"连接MySQL数据库失败: {e}")
            return False
    
    def close_connection(self):
        """关闭数据库连接"""
        if self.connection and self.connection.is_connected():
            self.connection.close()
            logger.info("数据库连接已关闭")
    
    def get_excel_sheets(self) -> List[str]:
        """获取Excel文件中的所有Sheet名称"""
        try:
            excel_file = pd.ExcelFile(self.excel_path)
            sheets = excel_file.sheet_names
            logger.info(f"发现 {len(sheets)} 个Sheet: {sheets}")
            return sheets
        except Exception as e:
            logger.error(f"读取Excel文件失败: {e}")
            return []
    
    def read_sheet_data(self, sheet_name: str) -> Optional[pd.DataFrame]:
        """读取指定Sheet的数据"""
        try:
            df = pd.read_excel(self.excel_path, sheet_name=sheet_name)
            
            # 跳过空Sheet
            if IMPORT_CONFIG['skip_empty_sheets'] and df.empty:
                logger.warning(f"Sheet '{sheet_name}' 为空，跳过处理")
                return None
            
            logger.info(f"成功读取Sheet '{sheet_name}'，共 {len(df)} 行数据")

            # 保存原始列名，用于后续处理
            original_columns = df.columns.tolist()

            # 清理列名，但保持原始列名的映射关系
            cleaned_columns = []
            for col in original_columns:
                try:
                    cleaned_col = sanitize_column_name(col)
                    cleaned_columns.append(cleaned_col)
                except Exception as e:
                    logger.warning(f"清理列名 '{col}' 失败: {e}")
                    cleaned_columns.append('unknown_col')

            df.columns = cleaned_columns

            # 处理空值
            df = df.where(pd.notnull(df), None)

            return df
            
        except Exception as e:
            logger.error(f"读取Sheet '{sheet_name}' 失败: {e}")
            return None
    
    def analyze_dataframe(self, df: pd.DataFrame) -> Dict[str, str]:
        """分析DataFrame，返回列信息"""
        columns_info = {}
        
        for col in df.columns:
            col_type = detect_column_type(df[col], col)
            columns_info[col] = col_type
            logger.debug(f"列 '{col}' 检测为类型: {col_type}")
        
        return columns_info
    
    def create_table(self, table_name: str, columns_info: Dict[str, str]) -> bool:
        """创建数据表"""
        try:
            cursor = self.connection.cursor()
            
            # 生成创建表的SQL
            create_sql = create_table_sql(table_name, columns_info)
            logger.debug(f"创建表SQL: {create_sql}")
            
            cursor.execute(create_sql)
            self.connection.commit()
            
            # 为时间列创建索引（时间列统一命名为date）
            if IMPORT_CONFIG['create_index_on_datetime']:
                has_datetime = any(col_type == "DATETIME" for col_type in columns_info.values())
                if has_datetime:
                    # MySQL不支持IF NOT EXISTS语法，需要先检查索引是否存在
                    index_name = f"idx_{table_name}_date"
                    check_index_sql = f"""
                    SELECT COUNT(*) FROM information_schema.statistics
                    WHERE table_schema = DATABASE()
                    AND table_name = '{table_name}'
                    AND index_name = '{index_name}'
                    """
                    cursor.execute(check_index_sql)
                    index_exists = cursor.fetchone()[0] > 0

                    if not index_exists:
                        index_sql = f"CREATE INDEX {index_name} ON {table_name}(date)"
                        try:
                            cursor.execute(index_sql)
                            self.connection.commit()
                            logger.info(f"为表 {table_name} 的date列创建索引")
                        except Error as e:
                            logger.warning(f"创建索引失败: {e}")
                    else:
                        logger.info(f"表 {table_name} 的date列索引已存在")
            
            cursor.close()
            logger.info(f"表 '{table_name}' 创建成功")
            return True
            
        except Error as e:
            logger.error(f"创建表 '{table_name}' 失败: {e}")
            return False
    
    def table_exists(self, table_name: str) -> bool:
        """检查表是否存在"""
        try:
            cursor = self.connection.cursor()
            cursor.execute(f"SHOW TABLES LIKE '{table_name}'")
            result = cursor.fetchone()
            cursor.close()
            return result is not None
        except Error as e:
            logger.error(f"检查表是否存在失败: {e}")
            return False
    
    def get_existing_data_times(self, table_name: str, datetime_col: str) -> List[datetime]:
        """获取数据库中已存在的时间数据（时间列统一为date）"""
        try:
            cursor = self.connection.cursor()
            # 时间列在数据库中统一命名为date
            cursor.execute(f"SELECT DISTINCT date FROM {table_name} WHERE date IS NOT NULL")
            results = cursor.fetchall()
            cursor.close()

            existing_times = [row[0] for row in results if row[0] is not None]
            logger.info(f"表 '{table_name}' 中已存在 {len(existing_times)} 个不同的时间点")
            return existing_times

        except Error as e:
            logger.error(f"获取已存在时间数据失败: {e}")
            return []
    
    def delete_existing_data_by_time(self, table_name: str, datetime_col: str, times_to_delete: List[datetime]) -> bool:
        """删除指定时间的数据（时间列统一为date）"""
        if not times_to_delete:
            return True

        try:
            cursor = self.connection.cursor()

            # 构建删除SQL，时间列在数据库中统一命名为date
            placeholders = ','.join(['%s'] * len(times_to_delete))
            delete_sql = f"DELETE FROM {table_name} WHERE date IN ({placeholders})"

            cursor.execute(delete_sql, times_to_delete)
            deleted_count = cursor.rowcount
            self.connection.commit()
            cursor.close()

            logger.info(f"删除了 {deleted_count} 条重复时间的数据")
            return True

        except Error as e:
            logger.error(f"删除重复时间数据失败: {e}")
            self.connection.rollback()
            return False

    def insert_data(self, table_name: str, df: pd.DataFrame, columns_info: Dict[str, str]) -> bool:
        """插入数据到数据库"""
        try:
            cursor = self.connection.cursor()

            # 准备插入数据
            data_list = prepare_data_for_insert(df, columns_info)

            if not data_list:
                logger.warning("没有数据需要插入")
                return True

            # 构建插入SQL，使用数据库实际列名（时间列为date）
            db_columns = get_database_column_names(columns_info)
            columns_str = ', '.join(db_columns)
            placeholders = ', '.join(['%s'] * len(db_columns))
            insert_sql = f"INSERT INTO {table_name} ({columns_str}) VALUES ({placeholders})"

            # 批量插入
            batch_size = IMPORT_CONFIG['batch_size']
            total_inserted = 0

            for i in range(0, len(data_list), batch_size):
                batch_data = data_list[i:i + batch_size]
                cursor.executemany(insert_sql, batch_data)
                total_inserted += len(batch_data)

                if i % (batch_size * 10) == 0:  # 每10个批次提交一次
                    self.connection.commit()
                    logger.info(f"已插入 {total_inserted}/{len(data_list)} 条数据")

            self.connection.commit()
            cursor.close()

            logger.info(f"成功插入 {total_inserted} 条数据到表 '{table_name}'")
            return True

        except Error as e:
            logger.error(f"插入数据失败: {e}")
            self.connection.rollback()
            return False

    def process_sheet_with_smart_update(self, sheet_name: str, table_name: str) -> bool:
        """处理单个Sheet，支持智能更新"""
        logger.info(f"开始处理Sheet: '{sheet_name}' -> 表: '{table_name}'")

        # 读取Sheet数据
        df = self.read_sheet_data(sheet_name)
        if df is None or df.empty:
            return False

        # 分析数据结构
        columns_info = self.analyze_dataframe(df)

        # 创建表（如果不存在）
        if not self.table_exists(table_name):
            if not self.create_table(table_name, columns_info):
                return False
        else:
            logger.info(f"表 '{table_name}' 已存在")

        # 查找时间列
        datetime_col = get_datetime_column(df)

        if datetime_col:
            logger.info(f"发现时间列: '{datetime_col}'")

            # 转换时间列为datetime类型
            try:
                df[datetime_col] = pd.to_datetime(df[datetime_col])
            except Exception as e:
                logger.warning(f"时间列转换失败: {e}")

            # 获取Excel中的时间数据
            excel_times = df[datetime_col].dropna().unique()

            # 获取数据库中已存在的时间数据
            existing_times = self.get_existing_data_times(table_name, datetime_col)

            # 找出重复的时间点
            excel_times_set = set(pd.to_datetime(excel_times))
            existing_times_set = set(existing_times)
            duplicate_times = list(excel_times_set.intersection(existing_times_set))

            if duplicate_times:
                logger.info(f"发现 {len(duplicate_times)} 个重复时间点，将用Excel数据替换")
                # 删除重复时间的数据
                if not self.delete_existing_data_by_time(table_name, datetime_col, duplicate_times):
                    return False
        else:
            logger.info("未发现时间列，将直接插入所有数据")

        # 插入数据
        return self.insert_data(table_name, df, columns_info)

    def import_all_sheets(self) -> bool:
        """导入所有Sheet"""
        logger.info(f"开始导入Excel文件: {self.excel_path}")

        # 检查Excel文件是否存在
        if not os.path.exists(self.excel_path):
            logger.error(f"Excel文件不存在: {self.excel_path}")
            return False

        # 创建数据库连接
        if not self.create_connection():
            return False

        try:
            # 获取所有Sheet
            sheets = self.get_excel_sheets()
            if not sheets:
                logger.error("没有找到任何Sheet")
                return False

            success_count = 0
            total_sheets = len(sheets)

            for sheet_name in sheets:
                # 生成表名
                table_name = sanitize_table_name(sheet_name)
                logger.info(f"处理Sheet: '{sheet_name}' -> 表名: '{table_name}'")

                # 处理Sheet
                if self.process_sheet_with_smart_update(sheet_name, table_name):
                    success_count += 1
                    logger.info(f"Sheet '{sheet_name}' 处理成功")
                else:
                    logger.error(f"Sheet '{sheet_name}' 处理失败")

            logger.info(f"导入完成！成功处理 {success_count}/{total_sheets} 个Sheet")
            return success_count == total_sheets

        except Exception as e:
            logger.error(f"导入过程中发生错误: {e}")
            return False
        finally:
            self.close_connection()

    def verify_import(self) -> bool:
        """验证导入结果"""
        if not self.create_connection():
            return False

        try:
            cursor = self.connection.cursor()

            # 获取所有表
            cursor.execute("SHOW TABLES")
            tables = cursor.fetchall()

            logger.info("导入验证结果:")
            logger.info("=" * 50)

            for (table_name,) in tables:
                # 获取表的记录数
                cursor.execute(f"SELECT COUNT(*) FROM {table_name}")
                count = cursor.fetchone()[0]

                # 获取表结构
                cursor.execute(f"DESCRIBE {table_name}")
                columns = cursor.fetchall()

                logger.info(f"表: {table_name}")
                logger.info(f"  记录数: {count}")
                logger.info(f"  列数: {len(columns)}")

                # 显示前几条记录
                cursor.execute(f"SELECT * FROM {table_name} LIMIT 3")
                sample_data = cursor.fetchall()
                if sample_data:
                    logger.info("  样本数据:")
                    for i, row in enumerate(sample_data, 1):
                        logger.info(f"    记录{i}: {row}")

                logger.info("-" * 30)

            cursor.close()
            return True

        except Error as e:
            logger.error(f"验证导入结果失败: {e}")
            return False
        finally:
            self.close_connection()
