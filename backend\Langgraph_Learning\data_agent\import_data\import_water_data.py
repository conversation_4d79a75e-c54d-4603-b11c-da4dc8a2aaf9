#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
水位数据导入MySQL数据库程序
将 Langgraph_Learning/data_agent/water-data.xlsx 数据导入到本地MySQL数据库的agent schema中
"""

import pandas as pd
import mysql.connector
from mysql.connector import Error
import sys
from datetime import datetime
import logging
from db_config import DB_CONFIG, EXCEL_FILE_PATH, TABLE_NAME

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def create_connection():
    """创建MySQL数据库连接"""
    try:
        connection = mysql.connector.connect(**DB_CONFIG)
        if connection.is_connected():
            logger.info("成功连接到MySQL数据库")
            return connection
    except Error as e:
        logger.error(f"连接MySQL数据库失败: {e}")
        return None

def create_water_data_table(connection):
    """创建水位数据表"""
    create_table_query = f"""
    CREATE TABLE IF NOT EXISTS {TABLE_NAME} (
        id INT AUTO_INCREMENT PRIMARY KEY,
        date DATETIME NOT NULL,
        yichang DECIMAL(10,2),
        zhicheng DECIMAL(10,2),
        shashi DECIMAL(10,2),
        jianli DECIMAL(10,2),
        chenglingji DECIMAL(10,2),
        shimen DECIMAL(10,2),
        taoyuan DECIMAL(10,2),
        taojiang DECIMAL(10,2),
        xiangtan DECIMAL(10,2),
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        INDEX idx_date (date)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
    """
    
    try:
        cursor = connection.cursor()
        cursor.execute(create_table_query)
        connection.commit()
        logger.info("水位数据表创建成功或已存在")
        cursor.close()
        return True
    except Error as e:
        logger.error(f"创建表失败: {e}")
        return False

def read_excel_data():
    """读取Excel文件数据"""
    try:
        df = pd.read_excel(EXCEL_FILE_PATH)
        logger.info(f"成功读取Excel文件，共 {len(df)} 行数据")
        
        # 转换日期格式
        df['date'] = pd.to_datetime(df['date'])
        
        # 处理空值，将NaN替换为None
        df = df.where(pd.notnull(df), None)
        
        return df
    except Exception as e:
        logger.error(f"读取Excel文件失败: {e}")
        return None

def insert_data_to_mysql(connection, df):
    """将数据插入到MySQL数据库"""
    insert_query = f"""
    INSERT INTO {TABLE_NAME} (date, yichang, zhicheng, shashi, jianli, chenglingji,
                           shimen, taoyuan, taojiang, xiangtan)
    VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s)
    """
    
    try:
        cursor = connection.cursor()
        
        # 清空现有数据（可选）
        clear_choice = input("是否清空现有数据？(y/n): ").lower()
        if clear_choice == 'y':
            cursor.execute(f"DELETE FROM {TABLE_NAME}")
            logger.info("已清空现有数据")
        
        # 批量插入数据
        data_to_insert = []
        for index, row in df.iterrows():
            data_to_insert.append((
                row['date'],
                row['yichang'],
                row['zhicheng'],
                row['shashi'],
                row['jianli'],
                row['chenglingji'],
                row['shimen'],
                row['taoyuan'],
                row['taojiang'],
                row['xiangtan']
            ))
        
        cursor.executemany(insert_query, data_to_insert)
        connection.commit()
        
        logger.info(f"成功插入 {cursor.rowcount} 条数据")
        cursor.close()
        return True
        
    except Error as e:
        logger.error(f"插入数据失败: {e}")
        connection.rollback()
        return False

def verify_data(connection):
    """验证插入的数据"""
    try:
        cursor = connection.cursor()

        # 查询总记录数
        cursor.execute(f"SELECT COUNT(*) FROM {TABLE_NAME}")
        total_count = cursor.fetchone()[0]
        logger.info(f"数据库中总记录数: {total_count}")

        # 查询前5条记录
        cursor.execute(f"SELECT * FROM {TABLE_NAME} ORDER BY date LIMIT 5")
        records = cursor.fetchall()
        
        logger.info("前5条记录:")
        for record in records:
            logger.info(record)
        
        cursor.close()
        return True
        
    except Error as e:
        logger.error(f"验证数据失败: {e}")
        return False

def main():
    """主函数"""
    logger.info("开始导入水位数据到MySQL数据库")
    
    # 创建数据库连接
    connection = create_connection()
    if not connection:
        sys.exit(1)
    
    try:
        # 创建表
        if not create_water_data_table(connection):
            sys.exit(1)
        
        # 读取Excel数据
        df = read_excel_data()
        if df is None:
            sys.exit(1)
        
        # 插入数据
        if not insert_data_to_mysql(connection, df):
            sys.exit(1)
        
        # 验证数据
        verify_data(connection)
        
        logger.info("数据导入完成！")
        
    except Exception as e:
        logger.error(f"程序执行出错: {e}")
    finally:
        if connection.is_connected():
            connection.close()
            logger.info("数据库连接已关闭")

if __name__ == "__main__":
    main()
