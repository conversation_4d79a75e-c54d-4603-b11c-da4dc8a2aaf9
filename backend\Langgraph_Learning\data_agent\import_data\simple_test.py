#!/usr/bin/env python3
# -*- coding: utf-8 -*-

# 简单测试修复功能
import sys
import os
from pathlib import Path

# 添加当前目录到Python路径
current_dir = Path(__file__).parent
sys.path.insert(0, str(current_dir))

def test_basic_functions():
    print("开始基本功能测试...")
    
    try:
        from utils import sanitize_column_name, chinese_to_pinyin
        
        # 测试数字列名
        print("测试数字列名:")
        result1 = sanitize_column_name("2024")
        print(f"  '2024' -> '{result1}'")
        
        result2 = sanitize_column_name(123)
        print(f"  123 -> '{result2}'")
        
        result3 = sanitize_column_name("100%")
        print(f"  '100%' -> '{result3}'")
        
        # 测试中文转拼音
        print("\n测试中文转拼音:")
        result4 = chinese_to_pinyin("三峡")
        print(f"  '三峡' -> '{result4}'")
        
        result5 = chinese_to_pinyin(123)
        print(f"  123 -> '{result5}'")
        
        print("\n✅ 基本功能测试通过!")
        return True
        
    except Exception as e:
        print(f"❌ 基本功能测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    test_basic_functions()
