import pandas as pd
import numpy as np
from scipy.interpolate import interp1d
import matplotlib.pyplot as plt
import matplotlib
# import scienceplots
from matplotlib.ticker import FixedLocator, MultipleLocator, AutoMinorLocator, MaxNLocator

# --- 配置 ---
# 请确保这个路径是您存放原始数据Excel文件的正确路径
EXCEL_FILE_PATH = r'D:\tengshuchen\文档\WXWork\****************\Cache\File\2025-07\三峡水库调洪任务.xlsx'
# 定义输出结果的Excel文件名
OUTPUT_EXCEL_PATH = r'D:\tengshuchen\文档\WXWork\****************\Cache\File\2025-07\三峡水库调洪计算结果.xlsx'


def perform_flood_routing():
    """
    主函数，执行整个洪水演算过程，包括数据加载、计算、结果输出和绘图。
    """
    # --- 1. 初始化和数据加载 ---
    matplotlib.rcParams['font.sans-serif'] = ['SimHei']  # 设置中文字体为黑体
    matplotlib.rcParams['axes.unicode_minus'] = False  # 解决负号显示问题

    # 定义水库库容曲线数据
    storage_data = {
        'level_m': [131.0, 132.0, 133.0, 134.0, 135.0, 136.0, 137.0, 138.0, 139.0, 140.0, 141.0, 142.0, 143.0, 144.0,
                    145.0, 148.0, 150.0, 155.0, 156.0, 160.0, 162.0, 162.7, 165.0, 166.5, 170.0, 171.0, 175.0, 180.4],
        'storage_10e8_m3': [107.30, 111.40, 115.50, 119.70, 124.00, 128.40, 132.90, 137.50, 142.20, 147.00, 151.80,
                            156.70, 161.70, 166.60, 171.50, 186.48, 196.90, 228.00, 234.80, 262.00, 277.30, 282.60,
                            300.20, 312.70, 344.00, 353.80, 393.00, 450.70]
    }
    df_storage = pd.DataFrame(storage_data)
    df_storage['storage_m3'] = df_storage['storage_10e8_m3'] * 1e8

    # 定义泄流能力曲线数据
    discharge_data = {
        'level_m': [135.00, 140.00, 145.00, 150.00, 155.00, 160.00, 165.00, 170.00, 175.00, 180.00, 183.00],
        'q_max_m3s': [63300, 66600, 70500, 74600, 75900, 79900, 87300, 96000, 106700, 119300, 127400]
    }
    df_discharge = pd.DataFrame(discharge_data)

    # 创建插值函数
    V_from_Z = interp1d(df_storage['level_m'], df_storage['storage_m3'], kind='linear', fill_value="extrapolate")
    Z_from_V = interp1d(df_storage['storage_m3'], df_storage['level_m'], kind='linear', fill_value="extrapolate")
    Q_max_from_Z = interp1d(df_discharge['level_m'], df_discharge['q_max_m3s'], kind='linear', fill_value="extrapolate")

    # 从Excel加载洪水过程数据
    try:
        df_flood_raw = pd.read_excel(EXCEL_FILE_PATH, usecols="A:D")
        if df_flood_raw.empty:
            print(f"错误: 从 '{EXCEL_FILE_PATH}' 加载的数据为空")
            return

        df_flood = pd.DataFrame({
            'date': pd.to_datetime(df_flood_raw['时间'], errors='coerce', format='%m月%d日'),
            'hour': 8,
            'inflow_res': df_flood_raw['三峡入库洪水过程'],
            'inflow_yz': df_flood_raw['宜枝区间洪水过程'],
            'inflow_zc': df_flood_raw['宜螺区间洪水过程'].fillna(0)
        }).dropna(subset=['date'])

        print(f"成功从 '{EXCEL_FILE_PATH}' 加载 {len(df_flood)} 条洪水数据。")
        print("加载数据预览:")
        print(df_flood.head())

    except FileNotFoundError:
        print(f"错误: 未找到Excel文件 '{EXCEL_FILE_PATH}'。请检查路径是否正确。")
        return
    except Exception as e:
        print(f"读取或处理Excel文件时发生错误: {e}")
        return

    # --- 2. 核心演算函数 ---
    def route_flood(start_level, df_inflow, V_from_Z, Z_from_V, Q_max_from_Z):
        """
        根据给定的起调水位和入流过程，进行洪水演算。
        """
        df_hydro = df_inflow.copy()
        df_hydro['inflow_zc_d3'] = df_hydro['inflow_zc'].shift(-2).fillna(method='ffill')

        num_steps = len(df_hydro)
        levels = np.full(num_steps, np.nan)
        outflows = np.full(num_steps, np.nan)
        storages = np.full(num_steps, np.nan)
        delta_t = 24 * 3600

        # 设置初始条件 (t=0)
        levels[0] = start_level
        storages[0] = V_from_Z(levels[0])

        q_target_0 = 0
        if levels[0] < 158:
            q1 = 56700 - df_hydro['inflow_yz'].iloc[0]
            q2 = 60000 - df_hydro['inflow_zc_d3'].iloc[0]
            q_target_0 = min(q1, q2)
        elif 158 <= levels[0] < 171:
            q_target_0 = 56700 - df_hydro['inflow_yz'].iloc[0]
        else:
            q_target_0 = 80000 - df_hydro['inflow_yz'].iloc[0]

        q_target_0 = max(q_target_0, 25000)
        q_max_0 = Q_max_from_Z(levels[0])
        outflows[0] = min(q_target_0, q_max_0)

        # 循环计算每个时段
        for i in range(num_steps - 1):
            Z_i, V_i, I_i, O_i = levels[i], storages[i], df_hydro['inflow_res'].iloc[i], outflows[i]
            I_i_plus_1 = df_hydro['inflow_res'].iloc[i + 1]
            yz_flow_i_plus_1 = df_hydro['inflow_yz'].iloc[i + 1]
            zc_d3_flow_i_plus_1 = df_hydro['inflow_zc_d3'].iloc[i + 1]

            q_target_next = 0
            if Z_i < 158:
                q_target_next = min(56700 - yz_flow_i_plus_1, 60000 - zc_d3_flow_i_plus_1)
            elif 158 <= Z_i < 171:
                q_target_next = 56700 - yz_flow_i_plus_1
            else:
                q_target_next = 80000 - yz_flow_i_plus_1

            q_target_next = max(q_target_next, 25000)
            q_max_i = Q_max_from_Z(Z_i)
            O_i_plus_1 = min(q_target_next, q_max_i)

            delta_V = (I_i_plus_1 - O_i_plus_1) * delta_t
            V_i_plus_1 = V_i + delta_V
            Z_i_plus_1 = Z_from_V(V_i_plus_1)

            if Z_i_plus_1 < levels[0]:
                O_i_plus_1 = I_i_plus_1 - (V_from_Z(Z_i) - V_from_Z(levels[0])) / delta_t
                Z_i_plus_1 = levels[0]
                V_i_plus_1 = V_from_Z(Z_i_plus_1)

            storages[i + 1] = V_i_plus_1
            levels[i + 1] = Z_i_plus_1
            outflows[i + 1] = O_i_plus_1

        return pd.DataFrame({
            'datetime': df_hydro['date'],
            'water_level_m': levels,
            'outflow_m3s': outflows,
            'storage_m3': storages  # 也返回库容过程
        })

    # --- 3. 执行任务计算 ---
    start_levels_all = [145, 150, 155, 157]  # 所有需要计算的起调水位
    start_levels_selected = [145, 157]  # 需要输出和绘图的起调水位

    # -- 任务1 --
    results_task1 = {}
    print("\n" + "=" * 50)
    print("任务1: 原始洪水过程调洪计算")
    print("=" * 50)
    for z0 in start_levels_all:
        print(f"\n--- 起调水位: {z0:.2f} m ---")
        result_df = route_flood(z0, df_flood, V_from_Z, Z_from_V, Q_max_from_Z)
        results_task1[f'Z0={z0}'] = result_df
        peak_level = result_df['water_level_m'].max()
        peak_outflow = result_df['outflow_m3s'].max()
        print(f"最高调洪水位: {peak_level:.2f} m")
        print(f"最大出库流量: {peak_outflow:.0f} m³/s")

    # -- 任务2 --
    results_task2 = {}
    print("\n\n" + "=" * 50)
    print("任务2: 1.2倍放大洪水过程调洪计算")
    print("=" * 50)
    df_flood_scaled = df_flood.copy()
    scale_factor = 1.2
    for col in ['inflow_res', 'inflow_yz', 'inflow_zc']:
        df_flood_scaled[col] *= scale_factor
    for z0 in start_levels_all:
        print(f"\n--- 起调水位: {z0:.2f} m (洪水放大1.2倍) ---")
        result_df_scaled = route_flood(z0, df_flood_scaled, V_from_Z, Z_from_V, Q_max_from_Z)
        results_task2[f'Z0={z0}'] = result_df_scaled
        peak_level_scaled = result_df_scaled['water_level_m'].max()
        peak_outflow_scaled = result_df_scaled['outflow_m3s'].max()
        print(f"最高调洪水位: {peak_level_scaled:.2f} m")
        print(f"最大出库流量: {peak_outflow_scaled:.0f} m³/s")

    # --- 4. 【新功能】将指定结果写入Excel ---
    print("\n\n" + "=" * 50)
    print(f"正在将指定结果写入Excel文件: {OUTPUT_EXCEL_PATH}")
    print("=" * 50)
    try:
        # 创建一个Excel写入器
        with pd.ExcelWriter(OUTPUT_EXCEL_PATH, engine='openpyxl') as writer:
            # 准备一个用于合并所有结果的DataFrame
            df_to_excel = pd.DataFrame({'日期': df_flood['date']})

            # 遍历需要保存的任务和起调水位
            for task_name, task_results, inflow_df in [('任务一', results_task1, df_flood),
                                                       ('任务二', results_task2, df_flood_scaled)]:
                for z0 in start_levels_selected:
                    label = f'Z0={z0}'
                    if label in task_results:
                        res_df = task_results[label]
                        # 将出库流量和库水位添加到合并的DataFrame中
                        df_to_excel[f'{task_name}_{z0}m_出库流量(m³/s)'] = res_df['outflow_m3s']
                        df_to_excel[f'{task_name}_{z0}m_库水位(m)'] = res_df['water_level_m']

            # 将原始和放大后的入库流量也加入
            df_to_excel['任务一_入库流量(m³/s)'] = df_flood['inflow_res']
            df_to_excel['任务二_入库流量(m³/s)'] = df_flood_scaled['inflow_res']

            # 将合并后的DataFrame写入到一个名为"调洪演算结果"的sheet中
            df_to_excel.to_excel(writer, sheet_name='调洪演算结果', index=False)

        print(f"成功将结果写入到 '{OUTPUT_EXCEL_PATH}'")

    except Exception as e:
        print(f"写入Excel文件时发生错误: {e}")

    # --- 5. 【修改后】绘制流量过程对比图 ---
    print("\n\n" + "=" * 50)
    print("正在生成流量过程对比图...")
    print("=" * 50)

    all_results = {'任务1': results_task1, '任务2 (放大1.2倍)': results_task2}
    for task_name, task_results in all_results.items():
        # 绘制水位过程
        plt.figure(figsize=(14, 7))
        for label, df_res in task_results.items():
            plt.plot(df_res['datetime'], df_res['water_level_m'], marker='.', linestyle='-', label=f'水位 ({label})')
        plt.title(f'{task_name}: 不同起调水位下的库水位过程')
        plt.xlabel('日期')
        plt.ylabel('库水位 (米)')
        plt.legend()
        plt.grid(True)
        plt.xticks(rotation=30)
        # plt.tight_layout()
        plt.show()

        # 绘制流量过程
        plt.figure(figsize=(14, 7))
        for label, df_res in task_results.items():
            plt.plot(df_res['datetime'][1:], df_res['outflow_m3s'][1:], marker='.', linestyle='-',
                     label=f'流量 ({label})')
        plt.title(f'{task_name}: 不同起调水位下的出库流量过程')
        plt.xlabel('日期')
        plt.ylabel('出库流量 (立方米每秒)')
        plt.legend()
        plt.grid(True)
        plt.xticks(rotation=30)
        # plt.tight_layout()
        plt.show()


# --- 程序入口 ---
if __name__ == '__main__':
    perform_flood_routing()