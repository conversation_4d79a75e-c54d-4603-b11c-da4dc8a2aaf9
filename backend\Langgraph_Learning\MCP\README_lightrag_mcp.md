# LightRAG 知识图谱查询 MCP 服务

## 概述

这个MCP服务集成了LightRAG知识图谱查询功能，允许Agent通过已构建的知识图谱回答用户问题。该服务基于现有的LightRAG知识库，无需重新构建，直接使用已有的知识图谱数据。

## 功能特性

### 1. 知识图谱查询 (`knowledge_query`)
- **功能**: 查询已构建的LightRAG知识图谱
- **参数**:
  - `question` (str): 用户要查询的问题
  - `mode` (str): 查询模式，可选值：
    - `naive`: 简单查询模式
    - `local`: 局部查询模式
    - `global`: 全局查询模式
    - `hybrid`: 混合查询模式（推荐）
- **返回**: 基于知识图谱的回答

### 2. 知识库状态检查 (`check_knowledge_base_status`)
- **功能**: 检查知识库状态，确认是否已正确加载
- **参数**: 无
- **返回**: 知识库状态信息，包括文件存在性和大小

## 配置说明

### 1. 环境变量配置
服务使用以下环境变量（在`.env`文件中配置）：

```env
# LLM配置
LLM_MODEL=deepseek-chat
LLM_BINDING_API_KEY=your_api_key
LLM_BINDING_HOST=https://api.deepseek.com

# 嵌入模型配置
EMBEDDING_MODEL=dengcao/Qwen3-Embedding-4B:Q4_K_M
EMBEDDING_DIM=2560
MAX_EMBED_TOKENS=32768
EMBEDDING_BINDING_HOST=http://localhost:11434

# 日志配置
LOG_DIR=./logs
LOG_MAX_BYTES=10485760
LOG_BACKUP_COUNT=5
VERBOSE_DEBUG=false
```

### 2. 知识库路径
- 知识库位置: `LightRAG-1.4.3/examples/dickens/`
- 服务会自动加载该目录下的知识图谱文件

### 3. MCP服务配置
在`servers_config.json`中已添加：

```json
"lightragQuery": {
  "command": "python",
  "args": ["./Langgraph_Learning/MCP/lightrag_query.py"],
  "transport": "stdio"
}
```

## 使用方式

### 在Agent中的优先级
根据`agent_prompts.txt`的配置，当用户的问题可以通过知识库回答时，应优先使用此服务，而不是网络搜索。

### 示例查询
```python
# 查询示例
question = "蓄满年份，1月—2月份水库下泄流量按不小于6000m^3控制，3月—5月份的最小下泄流量应满足葛洲坝下游庙嘴水位不低于多少米?"
result = await knowledge_query(question, "hybrid")
```

## 文件结构

```
Langgraph_Learning/MCP/
├── lightrag_query.py          # 主要的MCP服务文件
├── test_lightrag_mcp.py       # 测试脚本
└── README_lightrag_mcp.md     # 本说明文件

LightRAG-1.4.3/examples/dickens/  # 知识库目录
├── graph_chunk_entity_relation.graphml
├── kv_store_full_docs.json
├── vdb_entities.json
└── vdb_relationships.json
```

## 测试

运行测试脚本验证服务功能：

```bash
cd Langgraph_Learning/MCP
python test_lightrag_mcp.py
```

## 注意事项

1. **依赖关系**: 确保LightRAG相关依赖已安装
2. **知识库**: 服务依赖于已构建的知识图谱，确保`dickens`目录下有完整的知识库文件
3. **环境变量**: 确保`.env`文件中的配置正确，特别是API密钥和模型配置
4. **优先级**: 在Agent使用中，知识库查询优先级高于网络搜索

## 故障排除

1. **导入错误**: 检查LightRAG路径是否正确添加到Python路径
2. **知识库加载失败**: 检查知识库文件是否存在且完整
3. **API调用失败**: 检查环境变量中的API配置是否正确
4. **嵌入模型错误**: 确保Ollama服务正在运行且模型已下载

## 更新日志

- 2024-01-XX: 初始版本，集成LightRAG知识图谱查询功能
- 支持多种查询模式（naive/local/global/hybrid）
- 添加知识库状态检查功能
- 配置相对路径支持，无需修改原有LightRAG代码
