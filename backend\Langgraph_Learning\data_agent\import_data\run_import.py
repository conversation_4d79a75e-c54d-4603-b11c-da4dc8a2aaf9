#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
通用Excel数据导入运行脚本
兼容原有水位数据导入功能，同时支持新的通用导入功能
"""

import sys
import os
from pathlib import Path

# 添加当前目录到Python路径
current_dir = Path(__file__).parent
sys.path.insert(0, str(current_dir))

try:
    from universal_excel_importer import UniversalExcelImporter
    UNIVERSAL_IMPORTER_AVAILABLE = True
except ImportError:
    UNIVERSAL_IMPORTER_AVAILABLE = False

try:
    from import_water_data import main as import_water_main
    WATER_IMPORTER_AVAILABLE = True
except ImportError:
    WATER_IMPORTER_AVAILABLE = False

def check_requirements():
    """检查运行环境和依赖"""
    print("正在检查运行环境...")

    # 检查必要的Python包
    required_packages = ['pandas', 'mysql.connector', 'openpyxl']
    missing_packages = []

    for package in required_packages:
        try:
            if package == 'mysql.connector':
                import mysql.connector
            else:
                __import__(package)
            print(f"✅ {package} 已安装")
        except ImportError:
            missing_packages.append(package)
            print(f"❌ {package} 未安装")

    if missing_packages:
        print(f"\n请安装缺失的包:")
        if 'mysql.connector' in missing_packages:
            print("pip install mysql-connector-python")
        if 'pandas' in missing_packages:
            print("pip install pandas")
        if 'openpyxl' in missing_packages:
            print("pip install openpyxl")
        return False

    return True

def choose_import_mode():
    """选择导入模式"""
    print("\n" + "="*50)
    print("选择导入模式")
    print("="*50)

    options = []

    if UNIVERSAL_IMPORTER_AVAILABLE:
        options.append(("1", "通用Excel导入（推荐）", "universal"))
        print("1. 通用Excel导入（推荐）")
        print("   - 支持多Sheet处理")
        print("   - 中文转拼音")
        print("   - 智能数据类型识别")
        print("   - 智能数据更新")

    if WATER_IMPORTER_AVAILABLE:
        options.append(("2", "原有水位数据导入", "water"))
        print("2. 原有水位数据导入")
        print("   - 专门用于水位数据")
        print("   - 固定表结构")

    if not options:
        print("❌ 没有可用的导入模式")
        return None

    while True:
        choice = input(f"\n请选择导入模式 (1-{len(options)}): ").strip()

        for option_key, option_desc, option_mode in options:
            if choice == option_key:
                return option_mode

        print("❌ 无效选择，请重新输入")

def get_excel_path():
    """获取Excel文件路径"""
    from db_config import EXCEL_FILE_PATH

    while True:
        excel_path = input(f"请输入Excel文件路径 (默认: {EXCEL_FILE_PATH}): ").strip()

        if not excel_path:
            excel_path = EXCEL_FILE_PATH

        # 支持相对路径
        if not os.path.isabs(excel_path):
            excel_path = os.path.join(current_dir.parent, excel_path)

        if os.path.exists(excel_path):
            print(f"✅ Excel文件存在: {excel_path}")
            return excel_path
        else:
            print(f"❌ Excel文件不存在: {excel_path}")
            retry = input("是否重新输入路径？(y/n): ").lower().strip()
            if retry != 'y':
                return None

def show_config_info(excel_path=None):
    """显示配置信息"""
    print("\n" + "="*50)
    print("配置信息")
    print("="*50)

    try:
        from db_config import DB_CONFIG
        print(f"数据库主机: {DB_CONFIG['host']}")
        print(f"数据库端口: {DB_CONFIG['port']}")
        print(f"数据库名称: {DB_CONFIG['database']}")
        print(f"用户名: {DB_CONFIG['user']}")
        print(f"密码: {'*' * len(DB_CONFIG['password']) if DB_CONFIG['password'] else '(未设置)'}")

        if excel_path:
            print(f"Excel文件路径: {excel_path}")

        # 如果是水位数据导入模式，显示表名
        if WATER_IMPORTER_AVAILABLE:
            try:
                from db_config import TABLE_NAME
                print(f"目标表名: {TABLE_NAME}")
            except:
                pass

    except Exception as e:
        print(f"读取配置失败: {e}")
        return False

    return True

def run_universal_import(excel_path):
    """运行通用导入"""
    try:
        importer = UniversalExcelImporter(excel_path=excel_path)

        # 预览Excel文件
        sheets = importer.get_excel_sheets()
        if sheets:
            print(f"\n发现 {len(sheets)} 个Sheet:")
            for i, sheet_name in enumerate(sheets, 1):
                from utils import sanitize_table_name
                table_name = sanitize_table_name(sheet_name)
                print(f"  {i}. '{sheet_name}' -> 表: '{table_name}'")

        # 执行导入
        success = importer.import_all_sheets()

        if success:
            print("\n🎉 通用导入完成！")
            # 验证导入结果
            verify = input("是否验证导入结果？(y/n): ").lower().strip()
            if verify == 'y':
                importer.verify_import()
        else:
            print("\n❌ 通用导入失败")

        return success

    except Exception as e:
        print(f"\n❌ 通用导入过程中发生错误: {e}")
        return False

def run_water_import():
    """运行水位数据导入"""
    try:
        import_water_main()
        print("\n🎉 水位数据导入完成！")
        return True
    except Exception as e:
        print(f"\n❌ 水位数据导入过程中发生错误: {e}")
        return False

def main():
    """主函数"""
    print("="*60)
    print("Excel数据导入MySQL数据库程序")
    print("="*60)

    # 检查运行环境
    if not check_requirements():
        print("\n❌ 环境检查失败，请解决上述问题后重试")
        sys.exit(1)

    # 选择导入模式
    import_mode = choose_import_mode()
    if not import_mode:
        print("\n❌ 没有可用的导入模式")
        sys.exit(1)

    # 获取Excel文件路径（通用导入模式需要）
    excel_path = None
    if import_mode == "universal":
        excel_path = get_excel_path()
        if not excel_path:
            print("\n❌ 未指定有效的Excel文件路径")
            sys.exit(1)

    # 显示配置信息
    if not show_config_info(excel_path):
        print("\n❌ 配置检查失败")
        sys.exit(1)

    # 确认是否继续
    print("\n" + "="*50)
    print("准备开始导入数据")
    print("="*50)

    confirm = input("确认要开始导入数据吗？(y/n): ").lower().strip()
    if confirm != 'y':
        print("操作已取消")
        sys.exit(0)

    # 运行导入程序
    print("\n开始导入数据...")
    try:
        if import_mode == "universal":
            success = run_universal_import(excel_path)
        elif import_mode == "water":
            success = run_water_import()
        else:
            print(f"❌ 未知的导入模式: {import_mode}")
            sys.exit(1)

        if success:
            print("\n🎉 数据导入完成！")
        else:
            print("\n❌ 数据导入失败")
            sys.exit(1)

    except KeyboardInterrupt:
        print("\n\n⚠️  用户中断操作")
        sys.exit(1)
    except Exception as e:
        print(f"\n❌ 导入过程中发生错误: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()
