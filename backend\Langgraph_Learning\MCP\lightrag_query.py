# lightrag_query_mcp.py

import os
import sys
import asyncio
import inspect
import logging
import logging.config
from pathlib import Path
from dotenv import load_dotenv
from mcp.server.fastmcp import FastMCP
from pydantic import BaseModel, Field
from typing import Annotated

# 添加LightRAG到Python路径
current_dir = Path(__file__).parent
workspace_root = current_dir.parent.parent
lightrag_path = workspace_root / "LightRAG-1.4.3"
sys.path.insert(0, str(lightrag_path))

# 导入LightRAG相关模块
from lightrag import LightRAG, QueryParam
from lightrag.llm.openai import openai_complete_if_cache
from lightrag.llm.ollama import ollama_embed
from lightrag.utils import EmbeddingFunc, logger, set_verbose_debug
from lightrag.kg.shared_storage import initialize_pipeline_status

# 加载环境变量 - 使用相对路径
env_path = workspace_root / ".env"
load_dotenv(dotenv_path=str(env_path), override=False)

# 初始化 MCP 服务器
mcp = FastMCP("LightRAGQueryServer")

# LightRAG工作目录 - 使用相对路径指向已有的知识库
WORKING_DIR = str(workspace_root / "LightRAG-1.4.3" / "examples" / "dickens")

# 全局RAG实例
_rag_instance = None

def configure_logging():
    """Configure logging for the application"""
    # Reset any existing handlers to ensure clean configuration
    for logger_name in ["uvicorn", "uvicorn.access", "uvicorn.error", "lightrag"]:
        logger_instance = logging.getLogger(logger_name)
        logger_instance.handlers = []
        logger_instance.filters = []

    # Get log directory path from environment variable or use current directory
    log_dir = os.getenv("LOG_DIR", os.getcwd())
    log_file_path = os.path.abspath(
        os.path.join(log_dir, "lightrag_mcp.log")
    )

    os.makedirs(os.path.dirname(log_dir), exist_ok=True)

    # Get log file max size and backup count from environment variables
    log_max_bytes = int(os.getenv("LOG_MAX_BYTES", 10485760))  # Default 10MB
    log_backup_count = int(os.getenv("LOG_BACKUP_COUNT", 5))  # Default 5 backups

    logging.config.dictConfig(
        {
            "version": 1,
            "disable_existing_loggers": False,
            "formatters": {
                "default": {
                    "format": "%(levelname)s: %(message)s",
                },
                "detailed": {
                    "format": "%(asctime)s - %(name)s - %(levelname)s - %(message)s",
                },
            },
            "handlers": {
                "console": {
                    "formatter": "default",
                    "class": "logging.StreamHandler",
                    "stream": "ext://sys.stderr",
                },
                "file": {
                    "formatter": "detailed",
                    "class": "logging.handlers.RotatingFileHandler",
                    "filename": log_file_path,
                    "maxBytes": log_max_bytes,
                    "backupCount": log_backup_count,
                    "encoding": "utf-8",
                },
            },
            "loggers": {
                "lightrag": {
                    "handlers": ["console", "file"],
                    "level": "INFO",
                    "propagate": False,
                },
            },
        }
    )

    # Set the logger level to INFO
    logger.setLevel(logging.INFO)
    # Enable verbose debug if needed
    set_verbose_debug(os.getenv("VERBOSE_DEBUG", "false").lower() == "true")


async def llm_model_func(
        prompt, system_prompt=None, history_messages=[], keyword_extraction=False, **kwargs
) -> str:
    return await openai_complete_if_cache(
        os.getenv("LLM_MODEL", "deepseek-chat"),
        prompt,
        system_prompt=system_prompt,
        history_messages=history_messages,
        api_key=os.getenv("LLM_BINDING_API_KEY") or os.getenv("OPENAI_API_KEY"),
        base_url=os.getenv("LLM_BINDING_HOST", "https://api.deepseek.com"),
        **kwargs,
    )


async def initialize_rag():
    """初始化RAG实例"""
    global _rag_instance
    
    if _rag_instance is not None:
        return _rag_instance
    
    rag = LightRAG(
        working_dir=WORKING_DIR,
        llm_model_func=llm_model_func,
        embedding_func=EmbeddingFunc(
            embedding_dim=int(os.getenv("EMBEDDING_DIM", "2560")),
            max_token_size=int(os.getenv("MAX_EMBED_TOKENS", "32768")),
            func=lambda texts: ollama_embed(
                texts,
                embed_model=os.getenv("EMBEDDING_MODEL", "dengcao/Qwen3-Embedding-4B:Q4_K_M"),
                host=os.getenv("EMBEDDING_BINDING_HOST", "http://localhost:11434"),
            ),
        ),
    )

    await rag.initialize_storages()
    await initialize_pipeline_status()
    
    _rag_instance = rag
    return rag


async def print_stream(stream):
    """处理流式输出"""
    result = ""
    async for chunk in stream:
        if chunk:
            result += chunk
    return result


description = """
当用户的问题可以通过知识图谱回答时，请优先调用该函数。
该函数用于查询已构建的LightRAG知识图谱，能够基于图谱中的实体关系和文档内容提供准确的答案。
支持多种查询模式：naive（简单）、local（局部）、global（全局）、hybrid（混合，推荐）。
适用于回答基于已有文档知识的问题，特别是需要理解实体关系和上下文的复杂问题。
"""

@mcp.tool()
async def knowledge_query(
    question: Annotated[str, Field(description="用户要查询的问题")],
    mode: Annotated[str, Field(description="查询模式：naive/local/global/hybrid，默认为hybrid", default="hybrid")]
) -> str:
    """
    查询LightRAG知识图谱并返回答案。
    
    :param question: 用户的问题
    :param mode: 查询模式，可选值：naive, local, global, hybrid（推荐）
    :return: 基于知识图谱的回答
    """
    try:
        # 初始化RAG实例
        rag = await initialize_rag()
        
        # 验证查询模式
        valid_modes = ["naive", "local", "global", "hybrid"]
        if mode not in valid_modes:
            mode = "hybrid"
        
        print(f"正在查询知识图谱: {question}")
        print(f"查询模式: {mode}")
        
        # 执行查询
        resp = await rag.aquery(
            question,
            param=QueryParam(mode=mode, stream=True),
        )
        
        # 处理响应
        if inspect.isasyncgen(resp):
            result = await print_stream(resp)
        else:
            result = str(resp)
        
        print("知识图谱查询完成")
        return result
        
    except Exception as e:
        error_msg = f"知识图谱查询失败: {str(e)}"
        print(error_msg)
        return error_msg


@mcp.tool()
async def check_knowledge_base_status() -> str:
    """
    检查知识库状态，确认是否已正确加载。
    
    :return: 知识库状态信息
    """
    try:
        if not os.path.exists(WORKING_DIR):
            return f"知识库目录不存在: {WORKING_DIR}"
        
        # 检查关键文件是否存在
        key_files = [
            "graph_chunk_entity_relation.graphml",
            "kv_store_full_docs.json",
            "vdb_entities.json",
            "vdb_relationships.json"
        ]
        
        missing_files = []
        existing_files = []
        
        for file in key_files:
            file_path = os.path.join(WORKING_DIR, file)
            if os.path.exists(file_path):
                file_size = os.path.getsize(file_path)
                existing_files.append(f"{file} ({file_size} bytes)")
            else:
                missing_files.append(file)
        
        status_info = f"知识库目录: {WORKING_DIR}\n"
        status_info += f"存在的文件: {', '.join(existing_files)}\n"
        
        if missing_files:
            status_info += f"缺失的文件: {', '.join(missing_files)}\n"
            status_info += "警告: 知识库可能未完全构建"
        else:
            status_info += "知识库状态: 正常"
        
        return status_info
        
    except Exception as e:
        return f"检查知识库状态失败: {str(e)}"


if __name__ == "__main__":
    # 配置日志
    configure_logging()
    print("LightRAG 知识图谱查询 MCP 服务已启动...")
    print(f"知识库目录: {WORKING_DIR}")
    mcp.run(transport='stdio')
