# data_extractor_mcp.py

import os
import json
import pymysql
import asyncio
import pandas as pd
from dotenv import load_dotenv
from mcp.server.fastmcp import FastMCP
from pydantic import BaseModel, Field
from typing import Annotated

# 加载环境变量
load_dotenv(override=True)

# 初始化 MCP 服务器
mcp = FastMCP("DataExtractorServer")

# --- 数据库连接配置 ---
HOST = os.getenv('HOST')
USER = os.getenv('USER')
MYSQL_PW = os.getenv('MYSQL_PW')
DB_NAME = os.getenv('DB_NAME')
PORT = int(os.getenv('PORT', 3306))

def _extract_data_to_json(sql_query: str) -> str:
    """同步执行 SQL 查询并返回 JSON 格式数据的内部函数"""
    try:
        connection = pymysql.connect(
            host=HOST, user=USER, passwd=MYSQL_PW, db=DB_NAME, port=PORT, charset='utf8'
        )
        # 使用 pandas 读取数据
        df = pd.read_sql(sql_query, connection)
        connection.close()
        # 将 DataFrame 转换为 JSON 字符串（orient='records' 格式）
        return df.to_json(orient='records', force_ascii=False)
    except Exception as e:
        return json.dumps({"error": f"数据提取失败: {e}"})

@mcp.tool()
# async def extract_data(sql_query: str) -> str:
async def extract_data(
    sql_query: Annotated[str, Field(description="用于从 MySQL 提取数据的 SQL 查询语句。")]
) -> str:
    """
    从 MySQL 数据库提取数据，并将其作为 JSON 字符串返回。
    主 Agent 收到此 JSON 字符串后，可使用 `pd.read_json(StringIO(json_string))` 将其加载为 DataFrame。
    :param sql_query: 用于提取数据的 SQL 查询语句。
    :return: JSON 字符串形式的表格数据。如果出错，则返回包含 'error' 键的 JSON 字符串。
    """

    # 使用 asyncio.to_thread 在单独的线程中运行阻塞的数据库和 pandas 操作
    json_data = await asyncio.to_thread(_extract_data_to_json, sql_query)
    print("数据提取完成。")
    return json_data

if __name__ == "__main__":
    print("数据提取 MCP 服务已启动...")
    mcp.run(transport='stdio')