# 三峡调洪多场景计算功能说明

## 功能概述

本程序已升级支持多年份、多频率的洪水过程数据读取和计算，可以一次性计算多种条件下的调洪结果，并将结果分别写入Excel文件的不同工作表中。

## 配置文件说明

### input.json 配置结构

```json
{
  "scenarios": [
    {
      "year": "1954",
      "frequency": "0.01"
    },
    {
      "year": "1954", 
      "frequency": "0.001"
    }
  ],
  // 其他参数保持不变...
}
```

### scenarios 参数说明

- `scenarios`: 场景列表，包含多个计算场景
- `year`: 年份，对应Excel工作表名称中的年份部分（如"1954"对应"1954三峡"工作表）
- `frequency`: 频率，对应Excel工作表中的列标题（如"0.01"对应1%频率列）

## Excel数据文件结构要求

### 工作表命名规则

1. **三峡数据工作表**: `{年份}三峡` (如: "1954三峡", "1981三峡")
2. **宜枝区间工作表**: `{年份}宜枝` (如: "1954宜枝", "1981宜枝") 
3. **向家坝工作表**: `{年份}向家坝` (如: "1954向家坝", "1981向家坝")
4. **宜螺区间工作表**: `宜螺区间` (固定名称)

### 数据列结构

#### 三峡和宜枝工作表
- 第1行: 频率标题行 (0.001, 0.002, 0.005, 0.01, 0.02, 0.05)
- 第2行开始: 对应频率的流量数据

#### 向家坝工作表
- 第1列: 流量数据（程序固定读取第1列）

#### 宜螺区间工作表
- 第1列: 流量数据（程序固定读取第1列）

## 数据读取逻辑

对于每个场景（年份+频率组合）：

1. **三峡数据**: 从`{年份}三峡`工作表中读取`{频率}`列的数据
2. **宜枝数据**: 从`{年份}宜枝`工作表中读取`{频率}`列的数据  
3. **向家坝数据**: 从`{年份}向家坝`工作表中读取第1列数据
4. **宜螺区间数据**: 从`宜螺区间`工作表中读取第1列数据（如果启用）

## 输出结果

### 结果文件结构

程序会在`result.xlsx`文件中创建多个工作表，每个工作表对应一个计算场景：

- 工作表名称格式: `{年份}年{频率}频率`
- 例如: "1954年0.01频率", "1981年0.001频率"

### 每个工作表包含的列

- 汇总信息: 包含各种参数和计算结果汇总
- 三峡原始入库: 原始入库流量
- 上游拦蓄: 上游拦蓄流量
- 三峡入库: 调整后的入库流量
- 三峡初水位: 每个时段的初始水位
- 三峡初库容: 每个时段的初始库容
- 三峡下泄: 计算得出的下泄流量
- 三峡末库容: 每个时段的末库容
- 三峡末水位: 每个时段的末水位

## 使用示例

### 1. 配置多个场景

```json
{
  "scenarios": [
    {"year": "1954", "frequency": "0.01"},
    {"year": "1954", "frequency": "0.001"},
    {"year": "1981", "frequency": "0.01"},
    {"year": "1982", "frequency": "0.01"},
    {"year": "1998", "frequency": "0.01"}
  ]
}
```

### 2. 运行计算

```bash
python sanxia_scheduling.py
```

### 3. 查看结果

程序会输出每个场景的计算进度和结果汇总，最终生成包含多个工作表的Excel文件。

## 注意事项

1. **数据完整性**: 确保Excel文件中包含所有需要的工作表和数据列
2. **频率匹配**: 配置文件中的frequency值必须与Excel工作表中的列标题完全匹配
3. **数据长度**: 程序会自动处理不同工作表数据长度不一致的情况
4. **错误处理**: 如果某个场景计算失败，程序会跳过该场景继续计算其他场景

## 扩展功能

如需添加新的年份或频率：

1. 在Excel文件中添加对应的工作表和数据
2. 在`input.json`的`scenarios`数组中添加新的场景配置
3. 重新运行程序即可

这样的设计使得程序具有很好的扩展性，可以方便地添加新的计算场景而无需修改代码。
