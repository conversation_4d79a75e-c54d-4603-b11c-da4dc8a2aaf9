#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
通用Excel导入程序使用示例
演示如何使用通用Excel导入功能
"""

import pandas as pd
import numpy as np
from datetime import datetime, timedelta
import os
import sys
from pathlib import Path

# 添加当前目录到Python路径
current_dir = Path(__file__).parent
sys.path.insert(0, str(current_dir))

from universal_excel_importer import UniversalExcelImporter

def create_sample_excel():
    """创建示例Excel文件"""
    print("创建示例Excel文件...")
    
    # 创建示例数据
    base_date = datetime(2024, 1, 1)
    dates = [base_date + timedelta(days=i) for i in range(10)]
    
    # Sheet 1: 销售数据
    sales_data = {
        '日期': dates,
        '产品名称': ['产品A', '产品B', '产品C'] * 3 + ['产品A'],
        '销售数量': np.random.randint(10, 100, 10),
        '单价': np.random.uniform(50.0, 200.0, 10),
        '销售额': np.random.uniform(500.0, 10000.0, 10),
        '销售员': ['张三', '李四', '王五'] * 3 + ['张三'],
        '是否完成': [True, False, True, True, False, True, True, False, True, True]
    }
    
    # Sheet 2: 库存数据
    inventory_data = {
        '更新时间': dates,
        '产品编号': ['P001', 'P002', 'P003'] * 3 + ['P001'],
        '库存数量': np.random.randint(0, 1000, 10),
        '安全库存': np.random.randint(50, 200, 10),
        '库存价值': np.random.uniform(1000.0, 50000.0, 10),
        '仓库位置': ['A区', 'B区', 'C区'] * 3 + ['A区']
    }
    
    # Sheet 3: 客户数据
    customer_data = {
        '注册日期': dates,
        '客户姓名': ['客户A', '客户B', '客户C', '客户D', '客户E'] * 2,
        '年龄': np.random.randint(20, 60, 10),
        '消费金额': np.random.uniform(100.0, 5000.0, 10),
        '会员等级': ['普通', '银卡', '金卡', '钻石'] * 2 + ['普通', '银卡'],
        '联系电话': ['138****1234'] * 10,
        '是否活跃': [True] * 8 + [False] * 2
    }
    
    # 创建Excel文件
    sample_file = current_dir / 'sample_data.xlsx'
    
    with pd.ExcelWriter(sample_file, engine='openpyxl') as writer:
        pd.DataFrame(sales_data).to_excel(writer, sheet_name='销售数据', index=False)
        pd.DataFrame(inventory_data).to_excel(writer, sheet_name='库存数据', index=False)
        pd.DataFrame(customer_data).to_excel(writer, sheet_name='客户数据', index=False)
    
    print(f"示例Excel文件已创建: {sample_file}")
    return str(sample_file)

def example_basic_usage():
    """基本使用示例"""
    print("\n" + "="*50)
    print("基本使用示例")
    print("="*50)
    
    # 创建示例Excel文件
    excel_file = create_sample_excel()
    
    try:
        # 创建导入器实例
        importer = UniversalExcelImporter(excel_path=excel_file)
        
        # 获取Excel中的所有Sheet
        sheets = importer.get_excel_sheets()
        print(f"发现Sheet: {sheets}")
        
        # 预览每个Sheet的数据结构
        for sheet_name in sheets:
            print(f"\n分析Sheet: '{sheet_name}'")
            df = importer.read_sheet_data(sheet_name)
            
            if df is not None:
                print(f"  数据行数: {len(df)}")
                print(f"  数据列数: {len(df.columns)}")
                print(f"  列名: {list(df.columns)}")
                
                # 分析数据类型
                columns_info = importer.analyze_dataframe(df)
                print("  检测到的数据类型:")
                for col, dtype in columns_info.items():
                    print(f"    {col}: {dtype}")
        
        print("\n准备执行导入...")
        
        # 执行导入（注意：需要正确的数据库配置）
        try:
            success = importer.import_all_sheets()
            if success:
                print("✅ 导入成功！")
                
                # 验证导入结果
                print("\n验证导入结果:")
                importer.verify_import()
            else:
                print("❌ 导入失败")
        except Exception as e:
            print(f"⚠️  导入过程中出现错误: {e}")
            print("这可能是由于数据库连接配置问题导致的")
    
    finally:
        # 清理示例文件
        if os.path.exists(excel_file):
            os.remove(excel_file)
            print(f"\n已清理示例文件: {excel_file}")

def example_custom_config():
    """自定义配置示例"""
    print("\n" + "="*50)
    print("自定义配置示例")
    print("="*50)
    
    # 自定义数据库配置
    custom_db_config = {
        'host': 'localhost',
        'port': 3306,
        'database': 'test_db',
        'user': 'test_user',
        'password': 'test_password',
        'charset': 'utf8mb4',
        'autocommit': False,
        'use_unicode': True
    }
    
    excel_file = create_sample_excel()
    
    try:
        # 使用自定义配置创建导入器
        importer = UniversalExcelImporter(
            excel_path=excel_file,
            db_config=custom_db_config
        )
        
        print("使用自定义数据库配置:")
        print(f"  主机: {custom_db_config['host']}")
        print(f"  数据库: {custom_db_config['database']}")
        print(f"  用户: {custom_db_config['user']}")
        
        # 其他操作与基本示例相同...
        sheets = importer.get_excel_sheets()
        print(f"发现Sheet: {sheets}")
        
    finally:
        if os.path.exists(excel_file):
            os.remove(excel_file)

def example_error_handling():
    """错误处理示例"""
    print("\n" + "="*50)
    print("错误处理示例")
    print("="*50)
    
    # 测试不存在的文件
    print("1. 测试不存在的Excel文件:")
    try:
        importer = UniversalExcelImporter(excel_path="nonexistent_file.xlsx")
        sheets = importer.get_excel_sheets()
    except Exception as e:
        print(f"   预期错误: {e}")
    
    # 测试无效的数据库配置
    print("\n2. 测试无效的数据库配置:")
    invalid_db_config = {
        'host': 'invalid_host',
        'port': 3306,
        'database': 'invalid_db',
        'user': 'invalid_user',
        'password': 'invalid_password',
        'charset': 'utf8mb4'
    }
    
    excel_file = create_sample_excel()
    
    try:
        importer = UniversalExcelImporter(
            excel_path=excel_file,
            db_config=invalid_db_config
        )
        
        # 尝试连接数据库
        success = importer.create_connection()
        if not success:
            print("   预期错误: 数据库连接失败")
        
    finally:
        if os.path.exists(excel_file):
            os.remove(excel_file)

def main():
    """主函数"""
    print("="*60)
    print("通用Excel导入程序使用示例")
    print("="*60)
    
    try:
        # 基本使用示例
        example_basic_usage()
        
        # 自定义配置示例
        example_custom_config()
        
        # 错误处理示例
        example_error_handling()
        
        print("\n" + "="*60)
        print("所有示例演示完成！")
        print("="*60)
        
        print("\n使用提示:")
        print("1. 确保MySQL数据库服务正在运行")
        print("2. 在db_config.py中配置正确的数据库连接信息")
        print("3. 准备好要导入的Excel文件")
        print("4. 运行 python main.py 开始导入")
        
    except Exception as e:
        print(f"\n❌ 示例演示过程中发生错误: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
