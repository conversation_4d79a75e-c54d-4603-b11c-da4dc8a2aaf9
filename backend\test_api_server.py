"""
简化的测试API服务器
用于快速测试前端功能，不依赖完整的LangGraph Agent
"""

import os
import json
import logging
from pathlib import Path
from typing import Dict, Any
from fastapi import FastAPI, File, UploadFile, HTTPException
from fastapi.responses import FileResponse
from fastapi.middleware.cors import CORSMiddleware
from pydantic import BaseModel
from docx import Document as DocxDocument
import uvicorn

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# 创建FastAPI应用
app = FastAPI(title="Word编辑Agent测试API", version="1.0.0")

# 添加CORS中间件
app.add_middleware(
    CORSMiddleware,
    allow_origins=["http://localhost:3000"],  # 前端地址
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# 上传目录
upload_dir = Path("uploads")
upload_dir.mkdir(exist_ok=True)

# 确保上传目录存在并有写权限
try:
    test_file = upload_dir / "test_write.tmp"
    test_file.write_text("test")
    test_file.unlink()
    logger.info(f"上传目录准备就绪: {upload_dir.absolute()}")
except Exception as e:
    logger.error(f"上传目录权限问题: {e}")
    raise

class LoadDocumentRequest(BaseModel):
    file_path: str
    max_tokens_per_section: int = 30000

@app.post("/api/word/upload")
async def upload_word_document(file: UploadFile = File(...)):
    """上传Word文档"""
    try:
        logger.info(f"接收到文件上传请求: {file.filename}")
        
        # 验证文件类型
        if not file.filename or not file.filename.lower().endswith(('.doc', '.docx')):
            raise HTTPException(status_code=400, detail="请上传Word文档文件 (.doc 或 .docx)")
        
        # 保存文件
        file_path = upload_dir / file.filename
        
        with open(file_path, "wb") as buffer:
            content = await file.read()
            buffer.write(content)
        
        logger.info(f"文件上传成功: {file_path}, 大小: {len(content)} bytes")
        
        return {
            "success": True,
            "message": "文件上传成功",
            "filePath": str(file_path),
            "fileName": file.filename,
            "fileSize": len(content)
        }
        
    except Exception as e:
        logger.error(f"文件上传失败: {e}")
        raise HTTPException(status_code=500, detail=f"文件上传失败: {str(e)}")

def _heading_level(style_name: str) -> int | None:
    name = (style_name or "").lower()
    mapping = {
        "heading 1": 1, "heading 2": 2, "heading 3": 3, "heading 4": 4,
        "heading 5": 5, "heading 6": 6, "标题 1": 1, "标题 2": 2, "标题 3": 3,
        "标题 4": 4, "标题 5": 5, "标题 6": 6,
    }
    if name in mapping:
        return mapping[name]
    for prefix in ["heading", "标题"]:
        if name.startswith(prefix):
            try:
                num = int(name.replace(prefix, "").strip())
                if 1 <= num <= 6:
                    return num
            except ValueError:
                pass
    return None

def _parse_word_sections(file_path: str) -> dict:
    p = Path(file_path)
    if not p.exists():
        raise HTTPException(status_code=404, detail="文件不存在")
    try:
        doc = DocxDocument(str(p))
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"无法打开文档: {e}")

    sections = []
    current = None
    idx = 0

    def push():
        nonlocal current
        if not current:
            return
        content_text = "\n".join(current["_paras"]).strip()
        # 中文“文字数”采用去除空白后的字符计数
        if content_text:
            char_count = sum(1 for ch in content_text if not ch.isspace())
        else:
            char_count = 0
        current["content"] = content_text
        current["word_count"] = char_count
        current["char_count"] = char_count
        current.pop("_paras", None)
        sections.append(current)
        current = None

    for para in doc.paragraphs:
        txt = para.text or ""
        style_name = getattr(getattr(para, "style", None), "name", "") or ""
        lvl = _heading_level(style_name)
        if lvl is not None and txt.strip():
            push()
            idx += 1
            current = {"id": f"h{lvl}-{idx}", "title": txt.strip(), "level": lvl, "_paras": []}
        else:
            if current is None:
                idx += 1
                current = {"id": f"h1-{idx}", "title": "正文", "level": 1, "_paras": []}
            current["_paras"].append(txt)

    push()

    total_words = sum(s.get("word_count", 0) for s in sections)
    return {
        "success": True,
        "document_id": f"doc_{hash(str(p))}",
        "message": f"文档加载和分割成功，共 {len(sections)} 个章节",
        "document": {
            "name": p.name,
            "sections": sections,
            "total_words": total_words,
            "total_tokens": total_words,
        },
    }

@app.post("/api/word/load-and-split")
async def load_and_split_document(request: LoadDocumentRequest):
    """加载并分割Word文档（基于 python-docx）"""
    try:
        logger.info(f"接收到文档加载请求: {request.file_path}")
        result = _parse_word_sections(request.file_path)
        logger.info(f"文档分析完成: {len(result['document']['sections'])} 个章节")
        return result
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"文档加载失败: {e}")
        raise HTTPException(status_code=500, detail=f"文档加载失败: {str(e)}")

@app.post("/api/word/select-section")
async def select_section_for_editing(request: dict):
    """选择章节进行编辑（模拟实现）"""
    try:
        section_id = request.get("section_id")
        document_id = request.get("document_id")
        
        logger.info(f"选择章节进行编辑: {section_id}")
        
        return {
            "success": True,
            "message": f"已选择章节 {section_id} 进行编辑",
            "document_id": document_id,
            "section_id": section_id,
            "ready_for_editing": True
        }
        
    except Exception as e:
        logger.error(f"选择章节失败: {e}")
        raise HTTPException(status_code=500, detail=f"选择章节失败: {str(e)}")

@app.get("/api/health")
async def health_check():
    """健康检查"""
    return {
        "status": "healthy",
        "message": "Word编辑Agent测试API运行正常",
        "upload_dir": str(upload_dir.absolute()),
        "uploaded_files": [f.name for f in upload_dir.glob("*") if f.is_file()]
    }

@app.get("/api/word/download")
async def download_word_document(file_path: str):
    """下载完整Word文档（测试实现）"""
    try:
        path = Path(file_path)
        if not path.exists() or not path.is_file():
            raise HTTPException(status_code=404, detail="文件不存在")

        file_name = path.name
        ext = path.suffix.lower()
        if ext == ".docx":
            media_type = "application/vnd.openxmlformats-officedocument.wordprocessingml.document"
        elif ext == ".doc":
            media_type = "application/msword"
        else:
            media_type = "application/octet-stream"

        return FileResponse(
            path=path,
            filename=file_name,
            media_type=media_type,
        )
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"文件下载失败: {str(e)}")

@app.get("/")
async def root():
    """根路径"""
    return {
        "message": "Word编辑Agent测试API",
        "version": "1.0.0",
        "endpoints": [
            "/api/word/upload",
            "/api/word/load-and-split", 
            "/api/word/select-section",
            "/api/health"
        ]
    }

if __name__ == "__main__":
    print("🚀 启动Word编辑Agent测试API服务器...")
    print("📁 上传目录:", upload_dir.absolute())
    print("🌐 API地址: http://localhost:8001")
    print("📖 API文档: http://localhost:8001/docs")
    
    uvicorn.run(
        "test_api_server:app",
        host="0.0.0.0",
        port=8001,
        reload=True,
        log_level="info"
    )
