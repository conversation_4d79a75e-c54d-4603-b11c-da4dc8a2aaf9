# 三峡水利调度 MCP 服务器

这是一个基于 Model Context Protocol (MCP) 的三峡水利调度模型服务器，可以集成到 LangGraph Agent 中使用。

## 功能特性

### 1. 主要工具

- **sanxia_flood_routing**: 执行三峡水利调度计算
- **get_default_parameters**: 获取模型默认参数配置
- **validate_scenario_data**: 验证指定场景的数据可用性
- **list_available_scenarios**: 列出所有可用的洪水场景

### 2. 设计理念

- **数据分离**: 所有Excel数据通过数据库MCP获取，本服务专注于计算逻辑
- **参数灵活**: 支持用户自定义关键参数，其他参数使用默认值
- **场景驱动**: 支持单个或多个场景的批量计算
- **结果详细**: 返回完整的计算结果和汇总信息

## 使用方法

### 1. 环境准备

```bash
# 安装依赖
pip install mcp pandas numpy scipy openpyxl python-dotenv

# 确保flood_control模块可访问
# 本服务会自动导入 ../../flood_control/sanxia_scheduling.py
```

### 2. 启动MCP服务器

```bash
# 直接运行
python sch_mcp.py

# 或者作为MCP服务器启动
python -m sch_mcp
```

### 3. 在LangGraph中集成

```python
from langchain_mcp_adapters.client import MultiServerMCPClient
from langgraph.prebuilt import create_react_agent

# 配置MCP客户端
client = MultiServerMCPClient({
    "scheduling": {
        "command": "python",
        "args": ["./Langgraph_Learning/MCP/sch_mcp.py"],
        "transport": "stdio"
    }
})

# 获取工具并创建Agent
tools = await client.get_tools()
agent = create_react_agent(
    "anthropic:claude-3-5-sonnet-latest",
    tools
)

# 使用Agent
response = await agent.ainvoke({
    "messages": [{"role": "user", "content": "计算1954年0.01频率的洪水调度"}]
})
```

### 4. 工具使用示例

#### 获取默认参数
```python
result = await agent.ainvoke({
    "messages": [{"role": "user", "content": "获取三峡调度模型的默认参数"}]
})
```

#### 验证数据可用性
```python
result = await agent.ainvoke({
    "messages": [{"role": "user", "content": "验证1954年0.01频率的洪水数据是否可用"}]
})
```

#### 执行调度计算
```python
result = await agent.ainvoke({
    "messages": [{"role": "user", "content": "计算1954年0.01频率的洪水调度，初始水位161米，上游最大动用70，不考虑宜螺区间"}]
})
```

#### 批量计算多个场景
```python
result = await agent.ainvoke({
    "messages": [{"role": "user", "content": "计算1954年的0.01和0.001频率两个场景的洪水调度"}]
})
```

## 参数说明

### 必需参数

- **scenarios**: 调度场景列表
  - `year`: 洪水年份（如"1954"）
  - `frequency`: 洪水频率（如"0.01"）

### 可选参数

- **init_water_level**: 初始水位(m)，默认161
- **upstream_max**: 上游最大动用库容，默认70
- **yl_boolean**: 是否考虑宜螺区间，默认false

### 其他参数（使用默认值）

- sanxia_min_discharge: 三峡最小下泄流量
- xiangjiaba_min_discharge: 向家坝最小下泄流量
- jingjiang_max_level: 荆江特大洪水水位
- jingjiang_safe_discharge: 荆江安全泄量
- jingjiang_max_discharge: 荆江最大下泄流量
- yl_water_level: 宜螺控制水位
- yl_control_discharge: 宜螺控制流量
- time_length: 计算时长

## 返回结果格式

### 成功响应
```json
{
  "success": true,
  "message": "成功计算 1 个场景",
  "summary": [
    {
      "scenario": "1954年0.01频率",
      "max_water_level": 175.23,
      "min_water_level": 161.00,
      "max_discharge": 80000,
      "min_discharge": 25000,
      "total_inflow": 2500000,
      "total_outflow": 2400000
    }
  ],
  "detailed_results": {
    "1954年0.01频率": [
      // 详细的时序计算结果
    ]
  },
  "parameters_used": {
    "init_water_level": 161,
    "upstream_max": 70,
    "yl_boolean": false,
    "scenarios_count": 1
  }
}
```

### 错误响应
```json
{
  "success": false,
  "error": "具体错误信息",
  "message": "错误描述"
}
```

## 测试

运行测试脚本验证功能：

```bash
python test_sch_mcp.py
```

## 注意事项

1. **数据依赖**: 确保 `flood_control/parameter/` 目录下有完整的数据文件
2. **路径配置**: 服务器会自动解析相对路径，无需手动配置
3. **内存使用**: 大规模计算时注意内存使用情况
4. **错误处理**: 服务器会捕获并返回详细的错误信息

## 与其他MCP服务的协作

本服务设计为与数据库MCP服务协作：

1. **数据库MCP**: 负责从数据库读取Excel数据
2. **调度MCP**: 负责执行水利调度计算
3. **Agent**: 协调两个服务，实现完整的工作流

这种设计实现了数据获取和计算逻辑的分离，提高了系统的模块化程度。
