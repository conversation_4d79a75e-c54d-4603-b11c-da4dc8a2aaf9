import os
from dotenv import load_dotenv 
from langchain_deepseek import ChatDeepSeek
from typing import Annotated
from typing_extensions import TypedDict
from langgraph.graph import StateGraph, START, END
from langgraph.graph.message import add_messages
from langchain.chat_models import init_chat_model
from langgraph.prebuilt import create_react_agent
from langchain_tavily import TavilySearch
from langchain_core.tools import tool
from pydantic import BaseModel, Field
import requests,json

# 加载环境变量
load_dotenv(override=True)

# 内置搜索工具
search_tool = TavilySearch(max_results=5, topic="general")

# class WeatherQuery(BaseModel):
#     loc: str = Field(description="The location name of the city")
#
# @tool(args_schema = WeatherQuery)
# def get_weather(loc):
#     """
#     查询即时天气函数
#     :param loc: 必要参数，字符串类型，用于表示查询天气的具体城市名称，\
#     注意，中国的城市需要用对应城市的英文名称代替，例如如果需要查询北京市天气，则loc参数需要输入'Beijing'；
#     :return：OpenWeather API查询即时天气的结果，具体URL请求地址为：https://api.openweathermap.org/data/2.5/weather\
#     返回结果对象类型为解析之后的JSON格式对象，并用字符串形式进行表示，其中包含了全部重要的天气信息
#     """
#     # Step 1.构建请求
#     url = "https://api.openweathermap.org/data/2.5/weather"
#
#     # Step 2.设置查询参数
#     params = {
#         "q": loc,
#         "appid": os.getenv("OPENWEATHER_API_KEY"),    # 输入API key
#         "units": "metric",            # 使用摄氏度而不是华氏度
#         "lang":"zh_cn"                # 输出语言为简体中文
#     }
#
#     # Step 3.发送GET请求
#     response = requests.get(url, params=params)
#
#     # Step 4.解析响应
#     data = response.json()
#     return json.dumps(data)

# tools = [search_tool, get_weather]
tools = [search_tool]

# 创建模型
model = ChatDeepSeek(model="deepseek-chat")

prompt = """
你是一名乐于助人的智能助手，擅长根据用户的问题选择合适的工具来查询信息并回答。

当用户的问题涉及**天气信息**时，你应优先调用`get_weather`工具，查询用户指定城市的实时天气，并在回答中总结查询结果。

当用户的问题涉及**新闻、事件、实时动态**时，你应优先调用`search_tool`工具，检索相关的最新信息，并在回答中简要概述。

如果问题既包含天气又包含新闻，请先使用`get_weather`查询天气，再使用`search_tool`查询新闻，最后将结果合并后回复用户。

所有回答应使用**简体中文**，条理清晰、简洁友好。
"""


# 创建图
graph = create_react_agent(model=model, 
                           tools=tools, 
                           prompt=prompt)