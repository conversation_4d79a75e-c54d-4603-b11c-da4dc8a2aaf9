import pandas as pd
import numpy as np
import json
import os
from scipy.interpolate import interp1d


def load_config():
    """从parameter/input.json加载配置"""
    current_dir = os.path.dirname(os.path.abspath(__file__))
    config_path = os.path.join(current_dir, 'parameter', 'input.json')
    with open(config_path, 'r', encoding='utf-8') as f:
        config = json.load(f)
    return config


def load_capacity_curve(file_path):
    """水位库容曲线数据"""
    df = pd.read_excel(file_path)
    level_col = df.columns[0]
    capacity_col = df.columns[1]

    # 水位库容插值
    V_from_Z = interp1d(df[level_col], df[capacity_col] * 1e8, kind='linear', fill_value="extrapolate")
    Z_from_V = interp1d(df[capacity_col] * 1e8, df[level_col], kind='linear', fill_value="extrapolate")

    return V_from_Z, Z_from_V


def load_drainage_curve(file_path):
    """泄流能力曲线数据"""
    df = pd.read_excel(file_path)
    level_col = df.columns[0]
    drainage_col = df.columns[1]

    # 泄流能力插值
    drainage_from_level = interp1d(df[level_col], df[drainage_col], kind='linear', fill_value="extrapolate")

    return drainage_from_level


def perform_flood_routing():
    print("开始执行三峡调洪计算...")
    try:
        config = load_config()
        print("配置加载成功")
    except Exception as e:
        print(f"配置加载失败: {e}")
        return

    # 三峡参数
    init_water_level = config['init_water_level']
    sanxia_min_discharge = config['sanxia_min_discharge']
    upstream_max = config['upstream_max']
    # 向家坝
    xiangjiaba_min_discharge = config['xiangjiaba_min_discharge']
    # 荆江
    jingjiang_max_level = config['jingjiang_max_level']
    jingjiang_safe_discharge = config['jingjiang_safe_discharge']
    jingjiang_max_discharge = config['jingjiang_max_discharge']
    # 时间步长
    time_length = config['time_length']
    # 宜螺区间
    yl_boolean = config['yl_boolean']
    if yl_boolean:
        yl_water_level = config['yl_water_level']
        yl_control_discharge = config['yl_control_discharge']


    current_dir = os.path.dirname(os.path.abspath(__file__))
    input_file_path = os.path.join(current_dir, config['data_path']['flood_file_path'])
    capacity_file_path = os.path.join(current_dir, config['data_path']['capacity_file_path'])
    drainage_file_path = os.path.join(current_dir, config['data_path']['drainage_file_path'])
    output_file_path = os.path.join(current_dir, config['data_path']['output_file_path'])

    print(f"初始水位: {init_water_level} m")
    print(f"上游最大动用: {upstream_max} 亿m³")
    print(f"荆江特大洪水水位: {jingjiang_max_level} m")

    # 加载水位库容曲线和泄流能力曲线
    V_from_Z, Z_from_V = load_capacity_curve(capacity_file_path)
    drainage_from_level = load_drainage_curve(drainage_file_path)

    try:
        df_1954_sx = pd.read_excel(input_file_path, sheet_name='三峡')
        print(f"成功从 '{input_file_path}' 的 '三峡' 工作表加载 {len(df_1954_sx)} 条数据。")

        df_1954_yz = pd.read_excel(input_file_path, sheet_name='宜枝区间')
        print(f"成功从 '{input_file_path}' 的 '宜枝区间' 工作表加载 {len(df_1954_yz)} 条数据。")

        df_1954_yl = pd.read_excel(input_file_path, sheet_name='宜螺区间')
        print(f"成功从 '{input_file_path}' 的 '向家坝' 工作表加载 {len(df_1954_yl)} 条数据。")

        df_1954_xjb = pd.read_excel(input_file_path, sheet_name='向家坝')
        print(f"成功从 '{input_file_path}' 的 '向家坝' 工作表加载 {len(df_1954_xjb)} 条数据。")

        print("加载数据预览:")
        print(df_1954_sx.head())

    except FileNotFoundError:
        print(f"错误: 未找到Excel文件 '{input_file_path}'。请检查路径是否正确。")
        return
    except Exception as e:
        print(f"读取或处理Excel文件时发生错误: {e}")
        return

    B_col = df_1954_sx.iloc[1:, 9].values  # J列：三峡原始入库
    C_col = df_1954_yz.iloc[1:, 5].values  # AN列：区间

    yl_col = df_1954_yl.iloc[0:, 0].values

    R_col = df_1954_xjb.iloc[0:, 0].values  # R列：向家坝

    print(f"B列前5个值: {B_col[:5]}")
    print(f"C列前5个值: {C_col[:5]}")
    print(f"R列前5个值: {R_col[:5]}")
    # print(f"yl列前5个值: {yl_col[:5]}")

    # 过滤NaN值
    C_col = C_col[:len(R_col)]
    B_col = B_col[:len(R_col)]
    print(len(C_col))

    valid_indices = ~(np.isnan(B_col) | np.isnan(C_col) | np.isnan(R_col))
    B_col = B_col[valid_indices]
    C_col = C_col[valid_indices]
    R_col = R_col[valid_indices]
    print(f"过滤后数据长度: {len(B_col)}")

    # 数据长度
    n_rows = len(B_col)

    # 初始化结果数组
    D_col = np.zeros(n_rows)  # 上游预拦蓄
    E_col = np.zeros(n_rows)  # 上游动用库容
    F_col = np.zeros(n_rows)  # 上游拦蓄
    G_col = np.zeros(n_rows)  # 三峡入库
    H_col = np.zeros(n_rows)  # 三峡初水位
    I_col = np.zeros(n_rows)  # 三峡初库容
    J_col = np.zeros(n_rows)  # 枝城合成流量
    K_col = np.zeros(n_rows)  # 枝城控制流量
    K2_col = np.zeros(n_rows)  # 螺山控制流量
    L_col = np.zeros(n_rows)  # 中间值
    M_col = np.zeros(n_rows)  # 三峡下泄1
    M2_col = np.zeros(n_rows)  # 三峡下泄2
    N_col = np.zeros(n_rows)  # 三峡下泄
    O_col = np.zeros(n_rows)  # 三峡末库容
    P_col = np.zeros(n_rows)  # 三峡末水位

    for i in range(n_rows):
        j = i + 8
        if j >= n_rows:
            j = n_rows - 1

        # D列：上游预拦蓄
        if B_col[j] > 70000:
            D_col[i] = min(10000, R_col[i] - xiangjiaba_min_discharge)
        elif B_col[j] > 60000:
            D_col[i] = min(6000, R_col[i] - xiangjiaba_min_discharge)
        elif B_col[j] > 55000:
            D_col[i] = min(4000, R_col[i] - xiangjiaba_min_discharge)
        else:
            D_col[i] = 0

        # E列：上游动用库容
        if i == 0:
            E_col[i] = D_col[i] * 3600 * time_length / 100000000
        else:
            E_col[i] = D_col[i] * 3600 * time_length / 100000000 + E_col[i-1]

        # F列：上游拦蓄
        if E_col[i] <= upstream_max:
            F_col[i] = D_col[i]
        else:
            F_col[i] = 0

        # G列：三峡入库
        if i < 8:  # 前8个数据
            G_col[i] = B_col[i]
        else:
            G_col[i] = B_col[i] - F_col[i - 8] if len(F_col) > 1 else B_col[i]

        # H列：三峡初水位
        if i == 0:
            H_col[i] = init_water_level
        else:
            H_col[i] = P_col[i-1]

        # I列：三峡初库容（根据水位插值）
        I_col[i] = V_from_Z(H_col[i]) / 1e8

        # J列：枝城合成流量
        J_col[i] = G_col[i] + C_col[i]

        # K列：枝城控制流量
        if H_col[i] > jingjiang_max_level:
            if J_col[i] >= jingjiang_max_discharge:
                K_col[i] = jingjiang_max_discharge
            else:
                K_col[i] = J_col[i]
        else:
            K_col[i] = jingjiang_safe_discharge

        # L列：中间值
        L_col[i] = (J_col[i] - K_col[i]) * time_length * 3600 / 1e8

        # M列：三峡下泄1
        M_col[i] = K_col[i] - C_col[i]

        # 三峡下泄2
        if yl_boolean:
            M2_col[i] = yl_control_discharge - yl_col[i+8]

        # N列：三峡下泄
        if L_col[i] > 0:
            if yl_boolean:
                if H_col[i] < yl_water_level:
                    calculated_discharge = min(M_col[i], M2_col[i])
                else:
                    calculated_discharge = M_col[i]
            else:
                calculated_discharge = M_col[i]
        else:
            if H_col[i] > init_water_level:
                if i > 0:
                    initial_storage = V_from_Z(init_water_level) / 1e8  # 初始库容
                    if yl_boolean:
                        if H_col[i] < yl_water_level:
                            calculated_discharge = min(M_col[i], M2_col[i], (O_col[i-1] - initial_storage) * 1e8 / time_length / 3600 + G_col[i])
                        else:
                            calculated_discharge = min(M_col[i],
                                                       (O_col[i - 1] - initial_storage) * 1e8 / time_length / 3600 +
                                                       G_col[i])
                    else:
                        calculated_discharge = min(M_col[i], (O_col[i-1] - initial_storage) * 1e8 / time_length / 3600 + G_col[i])
                else:
                    calculated_discharge = G_col[i]
            else:
                calculated_discharge = G_col[i]

        # 根据H_col的三峡初水位和泄流能力曲线限制下泄量
        max_drainage_capacity = drainage_from_level(H_col[i])
        N_col[i] = min(calculated_discharge, max_drainage_capacity)

        # O列：三峡末库容
        O_col[i] = I_col[i] + (G_col[i] - N_col[i]) * 3600 * time_length / 100000000

        # P列：三峡末水位（根据库容插值，与初始水位取最大值）
        P_col[i] = max(Z_from_V(O_col[i] * 1e8), init_water_level)

    A12_max_level = np.max(P_col[1:]) if len(P_col) > 1 else np.max(P_col)  # 三峡最高水位
    A15_upstream_use = min(np.max(E_col), upstream_max)  # 上游动用
    A22_upstream_reserve = np.max(O_col[1:]) - 353.8 if len(O_col) > 1 else np.max(O_col) - 353.8  # 需上游水库预留库容

    print("\n" + "=" * 50)
    print("计算结果汇总")
    print("=" * 50)
    print(f"初始水位: {init_water_level:.2f} m")
    print(f"上游最大动用: {upstream_max:.2f}")
    print(f"荆江特大洪水水位: {jingjiang_max_level:.2f} m")
    print(f"三峡最高水位: {A12_max_level:.2f} m")
    print(f"上游动用: {A15_upstream_use:.2f}")
    print(f"荆江安全泄量: {jingjiang_safe_discharge}")
    print(f"荆江最大下泄: {jingjiang_max_discharge}")
    print(f"三峡最小下泄: {sanxia_min_discharge}")
    print(f"需上游水库预留库容: {A22_upstream_reserve:.2f}")
    print(f"向家坝最小下泄: {xiangjiaba_min_discharge}")
    print(f"计算时长: {time_length}")

    # --- 结果写入Excel ---
    print("\n\n" + "=" * 50)
    print(f"正在将计算结果写入Excel文件: {output_file_path}")
    print("=" * 50)
    try:
        summary_col = [None] * n_rows

        summary_col[0] = "初始水位"
        summary_col[1] = init_water_level
        summary_col[3] = "上游最大动用"
        summary_col[4] = upstream_max
        summary_col[6] = "荆江特大洪水水位"
        summary_col[7] = jingjiang_max_level
        summary_col[10] = "三峡最高水位"
        summary_col[11] = round(A12_max_level, 2)
        summary_col[13] = "上游动用"
        summary_col[14] = round(A15_upstream_use, 2)  # 修正索引
        summary_col[16] = jingjiang_safe_discharge
        summary_col[17] = jingjiang_max_discharge
        summary_col[18] = sanxia_min_discharge
        summary_col[20] = "需上游水库预留库容"
        summary_col[21] = round(A22_upstream_reserve, 2)
        summary_col[23] = "向家坝最小下泄"
        summary_col[24] = xiangjiaba_min_discharge
        summary_col[26] = "计算时长"
        summary_col[27] = time_length

        result_df = pd.DataFrame({
            '汇总信息': summary_col,
            '三峡原始入库': B_col,
            # '宜枝区间': C_col,
            # '上游动用库容': E_col.round(2),
            '上游拦蓄': F_col,
            '三峡入库': G_col,
            '三峡初水位': H_col.round(2),
            '三峡初库容': I_col.round(2),
            '三峡下泄': N_col,
            '三峡末库容': O_col.round(2),
            '三峡末水位': P_col.round(2)
        })

        # 写入Excel文件
        with pd.ExcelWriter(output_file_path, engine='openpyxl') as writer:
            result_df.to_excel(writer, sheet_name='计算结果', index=False)

        print(f"成功将结果写入到 '{output_file_path}'")
        print(f"包含 {len(result_df)} 行计算数据")

    except Exception as e:
        print(f"写入Excel文件时发生错误: {e}")

    print("\n计算完成！")


if __name__ == '__main__':
    perform_flood_routing()