#!/usr/bin/env python3
"""
测试LightRAG MCP服务的简单脚本
"""

import asyncio
import sys
from pathlib import Path

# 添加LightRAG到Python路径
current_dir = Path(__file__).parent
workspace_root = current_dir.parent.parent
lightrag_path = workspace_root / "LightRAG-1.4.3"
sys.path.insert(0, str(lightrag_path))

# 导入我们的MCP服务模块
from lightrag_query import knowledge_query, check_knowledge_base_status, configure_logging

async def test_mcp_service():
    """测试MCP服务的基本功能"""
    print("开始测试LightRAG MCP服务...")
    
    # 配置日志
    configure_logging()
    
    # 1. 检查知识库状态
    print("\n1. 检查知识库状态:")
    status = await check_knowledge_base_status()
    print(status)
    
    # 2. 测试知识查询
    print("\n2. 测试知识查询:")
    test_question = "蓄满年份，1月—2月份水库下泄流量按不小于6000m^3控制，3月—5月份的最小下泄流量应满足葛洲坝下游庙嘴水位不低于多少米?"
    
    try:
        result = await knowledge_query(test_question, "hybrid")
        print(f"查询结果: {result}")
    except Exception as e:
        print(f"查询失败: {e}")
    
    print("\n测试完成!")

if __name__ == "__main__":
    asyncio.run(test_mcp_service())
