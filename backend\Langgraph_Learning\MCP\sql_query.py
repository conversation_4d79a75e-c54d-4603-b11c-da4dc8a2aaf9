# sql_query_mcp.py

import os
import json
import pymysql
import asyncio
from dotenv import load_dotenv
from mcp.server.fastmcp import FastMCP
from pydantic import BaseModel, Field
from typing import Annotated

# 加载环境变量
load_dotenv(override=True)

# 初始化 MCP 服务器
mcp = FastMCP("SQLQueryServer")

# --- 数据库连接配置 ---
HOST = os.getenv('HOST')
USER = os.getenv('USER')
MYSQL_PW = os.getenv('MYSQL_PW')
DB_NAME = os.getenv('DB_NAME')
PORT = int(os.getenv('PORT', 3306))

def _run_sql_query(sql_query: str) -> str:
    """同步执行 SQL 查询的内部函数"""
    try:
        connection = pymysql.connect(
            host=HOST, user=USER, passwd=MYSQL_PW, db=DB_NAME, port=PORT, charset='utf8'
        )
        with connection.cursor() as cursor:
            cursor.execute(sql_query)
            results = cursor.fetchall()
        connection.close()
        # 将元组结果转换为 JSON 字符串返回
        return json.dumps(results, ensure_ascii=False, default=str)
    except pymysql.MySQLError as e:
        return json.dumps({"error": f"数据库查询失败: {e}"})
    except Exception as e:
        return json.dumps({"error": f"未知错误: {e}"})

description = """
当用户需要进行数据库查询工作时，请调用该函数。
该函数用于在指定MySQL服务器上运行一段SQL代码，完成数据查询相关工作，
并且当前函数是使用pymsql连接MySQL数据库。
本函数只负责运行SQL代码并进行数据查询，若要进行数据提取，则使用DataExtractor 服务。
"""

@mcp.tool()
# async def sql_inter(sql_query: str) -> str:
async def sql_inter(
    sql_query: Annotated[str, Field(description=description)]
) -> str:
    """
    在 MySQL 数据库上运行 SQL 查询并返回结果。
    此工具用于数据查询，返回的数据是 JSON 格式的字符串。
    如果需要将数据加载到 Python 环境进行分析，请使用 DataExtractor 服务。
    :param sql_query: 字符串形式的 SQL 查询语句。
    :return: JSON 字符串形式的查询结果。如果出错，则返回包含 'error' 键的 JSON 字符串。
    """
    # 使用 asyncio.to_thread 在单独的线程中运行阻塞的数据库IO操作
    result = await asyncio.to_thread(_run_sql_query, sql_query)
    print("SQL 查询执行完毕。")
    return result

if __name__ == "__main__":
    print("SQL 查询 MCP 服务已启动...")
    mcp.run(transport='stdio')