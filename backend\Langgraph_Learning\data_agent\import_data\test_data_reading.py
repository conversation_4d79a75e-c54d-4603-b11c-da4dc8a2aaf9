#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试数据读取功能
验证Excel文件能否正常读取和处理
"""

import pandas as pd
from db_config import EXCEL_FILE_PATH
import logging

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def test_read_excel():
    """测试读取Excel文件"""
    try:
        logger.info(f"正在读取Excel文件: {EXCEL_FILE_PATH}")
        df = pd.read_excel(EXCEL_FILE_PATH)
        
        logger.info(f"成功读取Excel文件，共 {len(df)} 行数据")
        logger.info(f"列名: {df.columns.tolist()}")
        logger.info(f"数据形状: {df.shape}")
        
        # 显示数据类型
        logger.info("数据类型:")
        for col in df.columns:
            logger.info(f"  {col}: {df[col].dtype}")
        
        # 转换日期格式
        df['date'] = pd.to_datetime(df['date'])
        logger.info("日期格式转换成功")
        
        # 处理空值
        null_counts = df.isnull().sum()
        logger.info("空值统计:")
        for col, count in null_counts.items():
            if count > 0:
                logger.info(f"  {col}: {count} 个空值")
        
        # 显示前5行数据
        logger.info("前5行数据:")
        print(df.head())
        
        # 显示日期范围
        logger.info(f"日期范围: {df['date'].min()} 到 {df['date'].max()}")
        
        return df
        
    except Exception as e:
        logger.error(f"读取Excel文件失败: {e}")
        return None

def test_data_processing(df):
    """测试数据处理"""
    if df is None:
        return False
    
    try:
        # 处理空值，将NaN替换为None
        df_processed = df.where(pd.notnull(df), None)
        logger.info("数据处理完成")
        
        # 准备插入数据的格式
        sample_data = []
        for index, row in df_processed.head().iterrows():
            sample_data.append((
                row['date'],
                row['yichang'],
                row['zhicheng'],
                row['shashi'],
                row['jianli'],
                row['chenglingji'],
                row['shimen'],
                row['taoyuan'],
                row['taojiang'],
                row['xiangtan']
            ))
        
        logger.info("样本数据格式:")
        for i, data in enumerate(sample_data):
            logger.info(f"  行 {i+1}: {data}")
        
        return True
        
    except Exception as e:
        logger.error(f"数据处理失败: {e}")
        return False

def main():
    """主函数"""
    logger.info("开始测试数据读取功能")
    
    # 测试读取Excel文件
    df = test_read_excel()
    
    if df is not None:
        # 测试数据处理
        if test_data_processing(df):
            logger.info("所有测试通过！数据可以正常处理")
        else:
            logger.error("数据处理测试失败")
    else:
        logger.error("Excel文件读取测试失败")

if __name__ == "__main__":
    main()
