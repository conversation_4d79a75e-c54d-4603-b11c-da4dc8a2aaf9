# sch_mcp.py - 三峡水利调度模型 MCP 服务器

import os
import json
import asyncio
import pandas as pd
import numpy as np
from typing import List, Dict, Any, Optional
from dotenv import load_dotenv
from mcp.server.fastmcp import FastMCP
from pydantic import BaseModel, Field
from typing import Annotated

# 导入水利调度模型的核心函数
import sys
sys.path.append(os.path.join(os.path.dirname(__file__), '..', '..', 'flood_control'))
from sanxia_scheduling import (
    load_config, load_capacity_curve, load_drainage_curve, 
    load_flood_data, calculate_single_scenario
)

# 加载环境变量
load_dotenv(override=True)

# 初始化 MCP 服务器
mcp = FastMCP("SanxiaSchedulingServer")

class ScenarioInput(BaseModel):
    """调度场景输入参数"""
    year: str = Field(description="洪水年份，如'1954'")
    frequency: str = Field(description="洪水频率，如'0.01'")

class SchedulingParameters(BaseModel):
    """调度参数"""
    init_water_level: float = Field(description="初始水位(m)")
    upstream_max: float = Field(description="上游最大动用库容")
    yl_boolean: bool = Field(description="是否考虑宜螺区间")

def _load_default_config():
    """加载默认配置"""
    try:
        current_dir = os.path.dirname(os.path.abspath(__file__))
        flood_control_dir = os.path.join(current_dir, '..', '..', 'flood_control')
        config_path = os.path.join(flood_control_dir, 'parameter', 'input.json')
        
        with open(config_path, 'r', encoding='utf-8') as f:
            config = json.load(f)
        return config
    except Exception as e:
        print(f"加载默认配置失败: {e}")
        return None

def _prepare_config_for_calculation(scenarios: List[Dict], 
                                   init_water_level: float = None,
                                   upstream_max: float = None, 
                                   yl_boolean: bool = None):
    """准备计算用的配置"""
    config = _load_default_config()
    if not config:
        raise ValueError("无法加载默认配置")
    
    # 更新场景
    config['scenarios'] = scenarios
    
    # 更新用户指定的参数
    if init_water_level is not None:
        config['init_water_level'] = init_water_level
    if upstream_max is not None:
        config['upstream_max'] = upstream_max
    if yl_boolean is not None:
        config['yl_boolean'] = yl_boolean
    
    return config

def _run_scheduling_calculation(scenarios: List[Dict], 
                               init_water_level: float = None,
                               upstream_max: float = None,
                               yl_boolean: bool = None) -> Dict[str, Any]:
    """同步执行水利调度计算的内部函数"""
    try:
        # 准备配置
        config = _prepare_config_for_calculation(scenarios, init_water_level, upstream_max, yl_boolean)
        
        # 获取文件路径
        current_dir = os.path.dirname(os.path.abspath(__file__))
        flood_control_dir = os.path.join(current_dir, '..', '..', 'flood_control')
        
        input_file_path = os.path.join(flood_control_dir, config['data_path']['flood_file_path'])
        capacity_file_path = os.path.join(flood_control_dir, config['data_path']['capacity_file_path'])
        drainage_file_path = os.path.join(flood_control_dir, config['data_path']['drainage_file_path'])
        output_file_path = os.path.join(flood_control_dir, config['data_path']['output_file_path'])

        # 加载水位库容曲线和泄流能力曲线
        V_from_Z, Z_from_V = load_capacity_curve(capacity_file_path)
        drainage_from_level = load_drainage_curve(drainage_file_path)
        
        # 存储所有场景的计算结果
        all_results = {}
        all_results_excel = {}
        calculation_summary = []
        
        # 循环计算每个场景
        for i, scenario in enumerate(scenarios, 1):
            year = scenario['year']
            frequency = scenario['frequency']
            
            print(f"正在计算第 {i}/{len(scenarios)} 个场景: {year}年 {frequency}频率")
            
            try:
                result_df, sheet_name = calculate_single_scenario(
                    year, frequency, config, V_from_Z, Z_from_V,
                    drainage_from_level, input_file_path
                )
                all_results_excel[sheet_name] = result_df
                
                # 将DataFrame转换为字典格式以便JSON序列化
                result_dict = result_df.to_dict('records')
                all_results[sheet_name] = result_dict
                
                # 提取关键结果指标
                summary = {
                    "scenario": f"{year}年{frequency}频率",
                    "max_water_level": float(result_df['三峡末水位'].max()),
                    "min_water_level": float(result_df['三峡末水位'].min()),
                    "max_discharge": float(result_df['三峡下泄'].max()),
                    "min_discharge": float(result_df['三峡下泄'].min()),
                    "total_inflow": float(result_df['三峡入库'].sum()),
                    "total_outflow": float(result_df['三峡下泄'].sum())
                }
                calculation_summary.append(summary)
                
                print(f"场景 {sheet_name} 计算完成")
                
            except Exception as e:
                error_msg = f"计算场景 {year}年{frequency}频率 时发生错误: {e}"
                print(error_msg)
                calculation_summary.append({
                    "scenario": f"{year}年{frequency}频率",
                    "error": str(e)
                })
                continue
        # 将所有结果写入Excel文件的不同工作表
        if all_results_excel:
            print(f"正在将所有计算结果写入Excel文件: {output_file_path}")

            try:
                with pd.ExcelWriter(output_file_path, engine='openpyxl') as writer:
                    for sheet_name, result_df in all_results_excel.items():
                        result_df.to_excel(writer, sheet_name=sheet_name, index=False)
                        print(f"已写入工作表: {sheet_name}")

                print(f"\n成功将所有结果写入到 '{output_file_path}'")
                print(f"共包含 {len(all_results_excel)} 个工作表")

            except Exception as e:
                print(f"写入Excel文件时发生错误: {e}")
        else:
            print("没有成功计算的结果可以写入")
        return {
            "success": True,
            "message": f"成功计算 {len(all_results)} 个场景",
            "summary": calculation_summary,
            "detailed_results": all_results,
            "parameters_used": {
                "init_water_level": config['init_water_level'],
                "upstream_max": config['upstream_max'],
                "yl_boolean": config['yl_boolean'],
                "scenarios_count": len(scenarios)
            }
        }
        
    except Exception as e:
        return {
            "success": False,
            "error": f"调度计算失败: {e}",
            "message": "请检查输入参数和数据文件"
        }

@mcp.tool()
async def sanxia_flood_routing(
    scenarios: Annotated[List[ScenarioInput], Field(description="调度场景列表，每个场景包含年份和频率")],
    init_water_level: Annotated[Optional[float], Field(description="初始水位(m)，默认使用配置文件中的值")] = None,
    upstream_max: Annotated[Optional[float], Field(description="上游最大动用库容，默认使用配置文件中的值")] = None,
    yl_boolean: Annotated[Optional[bool], Field(description="是否考虑宜螺区间，默认使用配置文件中的值")] = None
) -> str:
    """
    执行三峡水利调度计算。
    
    该工具用于计算指定场景下的三峡水库调洪过程，返回详细的调度结果。
    支持单个或多个场景的计算，可以自定义关键参数。
    
    Args:
        scenarios: 调度场景列表，每个场景需要指定年份和频率
        init_water_level: 初始水位(m)，如果不指定则使用默认值
        upstream_max: 上游最大动用库容，如果不指定则使用默认值  
        yl_boolean: 是否考虑宜螺区间，如果不指定则使用默认值
    
    Returns:
        JSON字符串形式的计算结果，包含汇总信息和详细数据
    """
    
    # 转换输入格式
    scenario_dicts = []
    for scenario in scenarios:
        if isinstance(scenario, dict):
            scenario_dicts.append(scenario)
        else:
            scenario_dicts.append({
                "year": scenario.year,
                "frequency": scenario.frequency
            })
    
    print(f"开始执行三峡调度计算，共 {len(scenario_dicts)} 个场景")
    
    # 使用 asyncio.to_thread 在单独的线程中运行阻塞的计算操作
    result = await asyncio.to_thread(
        _run_scheduling_calculation, 
        scenario_dicts, 
        init_water_level, 
        upstream_max, 
        yl_boolean
    )
    
    print("三峡调度计算执行完毕")
    return json.dumps(result, ensure_ascii=False, indent=2)

@mcp.tool()
async def get_default_parameters() -> str:
    """
    获取三峡调度模型的默认参数配置。
    
    该工具用于查看模型的默认参数设置，帮助用户了解可以调整的参数。
    
    Returns:
        JSON字符串形式的默认参数配置
    """
    try:
        config = _load_default_config()
        if config:
            # 只返回主要参数，不包含文件路径等内部配置
            parameters = {
                "init_water_level": config.get('init_water_level'),
                "sanxia_min_discharge": config.get('sanxia_min_discharge'),
                "upstream_max": config.get('upstream_max'),
                "xiangjiaba_min_discharge": config.get('xiangjiaba_min_discharge'),
                "jingjiang_max_level": config.get('jingjiang_max_level'),
                "jingjiang_safe_discharge": config.get('jingjiang_safe_discharge'),
                "jingjiang_max_discharge": config.get('jingjiang_max_discharge'),
                "yl_boolean": config.get('yl_boolean'),
                "yl_water_level": config.get('yl_water_level'),
                "yl_control_discharge": config.get('yl_control_discharge'),
                "time_length": config.get('time_length'),
                "example_scenarios": config.get('scenarios', [])[:3]  # 只显示前3个示例场景
            }
            return json.dumps({
                "success": True,
                "parameters": parameters,
                "description": {
                    "init_water_level": "初始水位(m)",
                    "sanxia_min_discharge": "三峡最小下泄流量(m³/s)",
                    "upstream_max": "上游最大动用库容",
                    "xiangjiaba_min_discharge": "向家坝最小下泄流量(m³/s)",
                    "jingjiang_max_level": "荆江特大洪水水位(m)",
                    "jingjiang_safe_discharge": "荆江安全泄量(m³/s)",
                    "jingjiang_max_discharge": "荆江最大下泄流量(m³/s)",
                    "yl_boolean": "是否考虑宜螺区间",
                    "yl_water_level": "宜螺控制水位(m)",
                    "yl_control_discharge": "宜螺控制流量(m³/s)",
                    "time_length": "计算时长(小时)"
                }
            }, ensure_ascii=False, indent=2)
        else:
            return json.dumps({
                "success": False,
                "error": "无法加载默认参数配置"
            }, ensure_ascii=False)
    except Exception as e:
        return json.dumps({
            "success": False,
            "error": f"获取默认参数失败: {e}"
        }, ensure_ascii=False)

@mcp.tool()
async def validate_scenario_data(
    year: Annotated[str, Field(description="洪水年份，如'1954'")],
    frequency: Annotated[str, Field(description="洪水频率，如'0.01'")]
) -> str:
    """
    验证指定年份和频率的洪水数据是否可用。

    该工具用于检查数据库或文件中是否存在指定场景的洪水数据，
    帮助用户在执行计算前确认数据的可用性。

    Args:
        year: 洪水年份
        frequency: 洪水频率

    Returns:
        JSON字符串形式的验证结果
    """
    try:
        # 加载默认配置获取文件路径
        config = _load_default_config()
        if not config:
            return json.dumps({
                "success": False,
                "error": "无法加载配置文件"
            }, ensure_ascii=False)

        current_dir = os.path.dirname(os.path.abspath(__file__))
        flood_control_dir = os.path.join(current_dir, '..', '..', 'flood_control')
        input_file_path = os.path.join(flood_control_dir, config['data_path']['flood_file_path'])

        # 检查文件是否存在
        if not os.path.exists(input_file_path):
            return json.dumps({
                "success": False,
                "error": f"洪水数据文件不存在: {input_file_path}"
            }, ensure_ascii=False)

        # 尝试加载数据验证可用性
        try:
            # 检查三峡数据
            sx_sheet_name = f"{year}三峡"
            df_sx = pd.read_excel(input_file_path, sheet_name=sx_sheet_name)

            # 检查频率列是否存在
            frequency_columns = df_sx.iloc[0]
            freq_available = any(str(col_val) == frequency for col_val in frequency_columns)

            if not freq_available:
                available_frequencies = [str(col) for col in frequency_columns if pd.notna(col)]
                return json.dumps({
                    "success": False,
                    "error": f"在{sx_sheet_name}中未找到频率{frequency}",
                    "available_frequencies": available_frequencies
                }, ensure_ascii=False)

            # 检查宜枝区间数据
            yz_sheet_name = f"{year}宜枝"
            df_yz = pd.read_excel(input_file_path, sheet_name=yz_sheet_name)
            yz_frequency_columns = df_yz.iloc[0]
            yz_freq_available = any(str(col_val) == frequency for col_val in yz_frequency_columns)

            # 检查向家坝数据
            xjb_sheet_name = f"{year}向家坝"
            df_xjb = pd.read_excel(input_file_path, sheet_name=xjb_sheet_name)

            # 获取数据长度信息
            sx_data_length = len(df_sx) - 1  # 减去标题行
            yz_data_length = len(df_yz) - 1
            xjb_data_length = len(df_xjb)

            return json.dumps({
                "success": True,
                "message": f"{year}年{frequency}频率的洪水数据验证通过",
                "data_info": {
                    "sanxia_sheet": sx_sheet_name,
                    "sanxia_data_length": sx_data_length,
                    "sanxia_frequency_available": True,
                    "yizhi_sheet": yz_sheet_name,
                    "yizhi_data_length": yz_data_length,
                    "yizhi_frequency_available": yz_freq_available,
                    "xiangjiaba_sheet": xjb_sheet_name,
                    "xiangjiaba_data_length": xjb_data_length,
                    "target_frequency": frequency
                },
                "warnings": [] if yz_freq_available else [f"宜枝区间数据中未找到频率{frequency}"]
            }, ensure_ascii=False, indent=2)

        except Exception as data_error:
            return json.dumps({
                "success": False,
                "error": f"数据验证失败: {data_error}",
                "suggestion": "请检查Excel文件中是否存在对应的工作表和数据"
            }, ensure_ascii=False)

    except Exception as e:
        return json.dumps({
            "success": False,
            "error": f"验证过程发生错误: {e}"
        }, ensure_ascii=False)

@mcp.tool()
async def list_available_scenarios() -> str:
    """
    列出数据文件中所有可用的洪水场景。

    该工具用于查看洪水数据文件中包含哪些年份和频率的数据，
    帮助用户选择合适的计算场景。

    Returns:
        JSON字符串形式的可用场景列表
    """
    try:
        # 加载默认配置获取文件路径
        config = _load_default_config()
        if not config:
            return json.dumps({
                "success": False,
                "error": "无法加载配置文件"
            }, ensure_ascii=False)

        current_dir = os.path.dirname(os.path.abspath(__file__))
        flood_control_dir = os.path.join(current_dir, '..', '..', 'flood_control')
        input_file_path = os.path.join(flood_control_dir, config['data_path']['flood_file_path'])

        # 检查文件是否存在
        if not os.path.exists(input_file_path):
            return json.dumps({
                "success": False,
                "error": f"洪水数据文件不存在: {input_file_path}"
            }, ensure_ascii=False)

        # 读取Excel文件的所有工作表名称
        excel_file = pd.ExcelFile(input_file_path)
        sheet_names = excel_file.sheet_names

        # 解析年份和数据类型
        available_scenarios = {}

        for sheet_name in sheet_names:
            if '三峡' in sheet_name:
                year = sheet_name.replace('三峡', '')
                if year not in available_scenarios:
                    available_scenarios[year] = {
                        "year": year,
                        "has_sanxia": False,
                        "has_yizhi": False,
                        "has_xiangjiaba": False,
                        "available_frequencies": []
                    }

                available_scenarios[year]["has_sanxia"] = True

                # 读取频率信息
                try:
                    df = pd.read_excel(input_file_path, sheet_name=sheet_name)
                    frequencies = [str(col) for col in df.iloc[0] if pd.notna(col) and str(col) != 'nan']
                    available_scenarios[year]["available_frequencies"] = frequencies
                except:
                    pass

            elif '宜枝' in sheet_name:
                year = sheet_name.replace('宜枝', '')
                if year not in available_scenarios:
                    available_scenarios[year] = {
                        "year": year,
                        "has_sanxia": False,
                        "has_yizhi": False,
                        "has_xiangjiaba": False,
                        "available_frequencies": []
                    }
                available_scenarios[year]["has_yizhi"] = True

            elif '向家坝' in sheet_name:
                year = sheet_name.replace('向家坝', '')
                if year not in available_scenarios:
                    available_scenarios[year] = {
                        "year": year,
                        "has_sanxia": False,
                        "has_yizhi": False,
                        "has_xiangjiaba": False,
                        "available_frequencies": []
                    }
                available_scenarios[year]["has_xiangjiaba"] = True

        # 转换为列表格式
        scenarios_list = list(available_scenarios.values())

        return json.dumps({
            "success": True,
            "message": f"找到 {len(scenarios_list)} 个年份的洪水数据",
            "available_scenarios": scenarios_list,
            "total_sheets": len(sheet_names),
            "file_path": input_file_path
        }, ensure_ascii=False, indent=2)

    except Exception as e:
        return json.dumps({
            "success": False,
            "error": f"获取可用场景失败: {e}"
        }, ensure_ascii=False)

if __name__ == "__main__":
    print("三峡水利调度 MCP 服务已启动...")
    mcp.run(transport='stdio')
