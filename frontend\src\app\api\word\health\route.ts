import { NextResponse } from 'next/server';

const BACKEND_URL = process.env.BACKEND_URL || 'http://localhost:8001';

export async function GET() {
  try {
    // 测试后端连接
    const response = await fetch(`${BACKEND_URL}/api/health`, {
      method: 'GET',
    });

    if (!response.ok) {
      return NextResponse.json(
        { 
          success: false, 
          message: '后端服务连接失败',
          backend_status: response.status 
        },
        { status: 502 }
      );
    }

    const backendData = await response.json();
    
    return NextResponse.json({
      success: true,
      message: '前端API代理正常',
      backend: backendData,
      frontend_status: 'healthy'
    });

  } catch (error) {
    console.error('Health check error:', error);
    return NextResponse.json(
      { 
        success: false, 
        message: '健康检查失败',
        error: error instanceof Error ? error.message : '未知错误'
      },
      { status: 500 }
    );
  }
}
