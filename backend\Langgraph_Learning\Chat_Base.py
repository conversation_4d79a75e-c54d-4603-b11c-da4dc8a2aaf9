import os
from dotenv import load_dotenv
load_dotenv(override=True)

DeepSeek_API_KEY = os.getenv("DEEPSEEK_API_KEY")
# print(DeepSeek_API_KEY)#可以通过打印查看

# from openai import OpenAI
# #初始化DeepSeek的API客户端
# client = OpenAI(api_key=DeepSeek_API_KEY, base_url="https://api.deepseek.com")
# #调用DeepSeek的API，生成回答
# response = client.chat.completions.create(
#     model="deepseek-chat",
#     messages=[
#         {"role": "system", "content": "你是乐于助人的助手，请根据用户的问题给出回答"},
#         {"role": "user", "content": "你好，请你介绍一下你自己。"},
#     ],
# )
# #打印模型最终的响应结果
# print(response.choices [0] .message.content)



from langchain.chat_models import init_chat_model
model = init_chat_model(model="deepseek-chat",model_provider="deepseek")
question="你好，请你介绍一下你自己。"
result = model.invoke(question)
print(result.content)