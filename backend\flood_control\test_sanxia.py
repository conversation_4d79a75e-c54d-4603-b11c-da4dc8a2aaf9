#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试修改后的三峡调洪程序
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from sanxia_scheduling import perform_flood_routing

if __name__ == '__main__':
    try:
        perform_flood_routing()
        print("程序执行成功！")
    except Exception as e:
        print(f"程序执行出错: {e}")
        import traceback
        traceback.print_exc()
