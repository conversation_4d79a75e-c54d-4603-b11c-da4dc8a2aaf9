{"mcpServers": {"weather": {"command": "python", "args": ["./Langgraph_Learning/MCP/weather_server.py"], "transport": "stdio"}, "dataExtractor": {"command": "python", "args": ["./Langgraph_Learning/MCP/data_extractor.py"], "transport": "stdio"}, "plotting": {"command": "python", "args": ["./Langgraph_Learning/MCP/plotting.py"], "transport": "stdio"}, "pythonInterpreter": {"command": "python", "args": ["./Langgraph_Learning/MCP/python_interpreter.py"], "transport": "stdio"}, "sqlQuery": {"command": "python", "args": ["./Langgraph_Learning/MCP/sql_query.py"], "transport": "stdio"}, "sanxiaScheduling": {"command": "python", "args": ["./Langgraph_Learning/MCP/sch_mcp.py"], "transport": "stdio"}, "lightragQuery": {"command": "python", "args": ["./Langgraph_Learning/MCP/lightrag_query.py"], "transport": "stdio"}}}