# 水位数据导入MySQL数据库

这个程序用于将 `Langgraph_Learning/data_agent/water-data.xlsx` 中的水位数据导入到本地MySQL数据库的 `agent` schema 中。

## 文件说明

- `import_water_data.py` - 主程序文件，包含完整的数据导入逻辑
- `db_config.py` - 数据库配置文件，可修改数据库连接参数
- `run_import.py` - 简化的运行脚本，提供友好的用户界面
- `test_data_reading.py` - 测试脚本，验证Excel数据读取功能
- `README_water_data_import.md` - 使用说明文档

## 数据结构

Excel文件包含以下列：
- `date` - 日期时间
- `yichang` - 宜昌水位数据
- `zhicheng` - 枝城水位数据
- `shashi` - 沙市水位数据
- `jianli` - 监利水位数据
- `chenglingji` - 城陵矶水位数据
- `shimen` - 石门水位数据
- `taoyuan` - 桃源水位数据
- `taojiang` - 桃江水位数据
- `xiangtan` - 湘潭水位数据

## 使用步骤

### 1. 安装依赖包

```bash
pip install mysql-connector-python openpyxl pandas
```

### 2. 配置数据库连接

编辑 `db_config.py` 文件，修改以下配置：

```python
DB_CONFIG = {
    'host': 'localhost',        # MySQL服务器地址
    'port': 3306,              # MySQL端口号
    'database': 'agent',       # 数据库名称
    'user': 'root',            # 用户名，请根据实际情况修改
    'password': 'your_password', # 密码，请根据实际情况修改
    'charset': 'utf8mb4',
    'autocommit': False,
    'use_unicode': True
}
```

### 3. 确保MySQL服务运行

确保你的MySQL服务正在运行，并且已经创建了 `agent` schema：

```sql
CREATE SCHEMA agent;
```

### 4. 运行导入程序

#### 方法一：使用简化脚本（推荐）

```bash
python run_import.py
```

这个脚本会：
- 自动检查运行环境和依赖包
- 显示当前配置信息
- 提供友好的用户界面
- 运行完整的导入流程

#### 方法二：直接运行主程序

```bash
python import_water_data.py
```

#### 方法三：测试数据读取功能

```bash
python test_data_reading.py
```

程序执行流程：
1. 连接到MySQL数据库
2. 创建 `water_data` 表（如果不存在）
3. 读取Excel文件数据
4. 询问是否清空现有数据
5. 将数据批量插入到数据库
6. 验证插入的数据

## 数据库表结构

程序会自动创建以下表结构：

```sql
CREATE TABLE IF NOT EXISTS water_data (
    id INT AUTO_INCREMENT PRIMARY KEY,
    date DATETIME NOT NULL,
    yichang DECIMAL(10,2),
    zhicheng DECIMAL(10,2),
    shashi DECIMAL(10,2),
    jianli DECIMAL(10,2),
    chenglingji DECIMAL(10,2),
    shimen DECIMAL(10,2),
    taoyuan DECIMAL(10,2),
    taojiang DECIMAL(10,2),
    xiangtan DECIMAL(10,2),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    INDEX idx_date (date)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
```

## 注意事项

1. 请确保MySQL服务正在运行
2. 请根据你的MySQL配置修改 `db_config.py` 中的用户名和密码
3. 程序会处理Excel中的空值，将其转换为数据库的NULL值
4. 程序支持批量插入，提高导入效率
5. 程序会在导入完成后验证数据，显示总记录数和前5条记录

## 错误处理

程序包含完整的错误处理机制：
- 数据库连接失败处理
- Excel文件读取失败处理
- 数据插入失败处理
- 自动回滚机制

## 日志记录

程序会输出详细的日志信息，包括：
- 数据库连接状态
- 表创建状态
- 数据读取进度
- 数据插入进度
- 验证结果
