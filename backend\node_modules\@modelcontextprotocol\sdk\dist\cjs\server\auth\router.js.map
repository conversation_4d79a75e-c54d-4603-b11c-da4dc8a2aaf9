{"version": 3, "file": "router.js", "sourceRoot": "", "sources": ["../../../../src/server/auth/router.ts"], "names": [], "mappings": ";;;;;;AAmHA,sCA0CC;AA8BD,sDAuBC;AAaD,oFAEC;AAjOD,sDAAkD;AAClD,wDAAqG;AACrG,kDAAwE;AACxE,0DAA4F;AAC5F,oDAAmF;AACnF,wDAAyD;AA6CzD,MAAM,cAAc,GAAG,CAAC,MAAW,EAAQ,EAAE;IAC3C,mHAAmH;IACnH,IAAI,MAAM,CAAC,QAAQ,KAAK,QAAQ,IAAI,MAAM,CAAC,QAAQ,KAAK,WAAW,IAAI,MAAM,CAAC,QAAQ,KAAK,WAAW,EAAE,CAAC;QACvG,MAAM,IAAI,KAAK,CAAC,0BAA0B,CAAC,CAAC;IAC9C,CAAC;IACD,IAAI,MAAM,CAAC,IAAI,EAAE,CAAC;QAChB,MAAM,IAAI,KAAK,CAAC,wCAAwC,MAAM,EAAE,CAAC,CAAC;IACpE,CAAC;IACD,IAAI,MAAM,CAAC,MAAM,EAAE,CAAC;QAClB,MAAM,IAAI,KAAK,CAAC,4CAA4C,MAAM,EAAE,CAAC,CAAC;IACxE,CAAC;AACH,CAAC,CAAA;AAEM,MAAM,mBAAmB,GAAG,CAAC,OAMnC,EAAiB,EAAE;;IAClB,MAAM,MAAM,GAAG,OAAO,CAAC,SAAS,CAAC;IACjC,MAAM,OAAO,GAAG,OAAO,CAAC,OAAO,CAAC;IAEhC,cAAc,CAAC,MAAM,CAAC,CAAC;IAEvB,MAAM,sBAAsB,GAAG,YAAY,CAAC;IAC5C,MAAM,cAAc,GAAG,QAAQ,CAAC;IAChC,MAAM,qBAAqB,GAAG,OAAO,CAAC,QAAQ,CAAC,YAAY,CAAC,cAAc,CAAC,CAAC,CAAC,WAAW,CAAC,CAAC,CAAC,SAAS,CAAC;IACrG,MAAM,mBAAmB,GAAG,OAAO,CAAC,QAAQ,CAAC,WAAW,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,SAAS,CAAC;IAEjF,MAAM,QAAQ,GAAkB;QAC9B,MAAM,EAAE,MAAM,CAAC,IAAI;QACnB,qBAAqB,EAAE,MAAA,OAAO,CAAC,uBAAuB,0CAAE,IAAI;QAE5D,sBAAsB,EAAE,IAAI,GAAG,CAAC,sBAAsB,EAAE,OAAO,IAAI,MAAM,CAAC,CAAC,IAAI;QAC/E,wBAAwB,EAAE,CAAC,MAAM,CAAC;QAClC,gCAAgC,EAAE,CAAC,MAAM,CAAC;QAE1C,cAAc,EAAE,IAAI,GAAG,CAAC,cAAc,EAAE,OAAO,IAAI,MAAM,CAAC,CAAC,IAAI;QAC/D,qCAAqC,EAAE,CAAC,oBAAoB,CAAC;QAC7D,qBAAqB,EAAE,CAAC,oBAAoB,EAAE,eAAe,CAAC;QAE9D,gBAAgB,EAAE,OAAO,CAAC,eAAe;QAEzC,mBAAmB,EAAE,mBAAmB,CAAC,CAAC,CAAC,IAAI,GAAG,CAAC,mBAAmB,EAAE,OAAO,IAAI,MAAM,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,SAAS;QAC3G,0CAA0C,EAAE,mBAAmB,CAAC,CAAC,CAAC,CAAC,oBAAoB,CAAC,CAAC,CAAC,CAAC,SAAS;QAEpG,qBAAqB,EAAE,qBAAqB,CAAC,CAAC,CAAC,IAAI,GAAG,CAAC,qBAAqB,EAAE,OAAO,IAAI,MAAM,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,SAAS;KAClH,CAAC;IAEF,OAAO,QAAQ,CAAA;AACjB,CAAC,CAAA;AAtCY,QAAA,mBAAmB,uBAsC/B;AAED;;;;;;;;;;;GAWG;AACH,SAAgB,aAAa,CAAC,OAA0B;IACtD,MAAM,aAAa,GAAG,IAAA,2BAAmB,EAAC,OAAO,CAAC,CAAC;IAEnD,MAAM,MAAM,GAAG,iBAAO,CAAC,MAAM,EAAE,CAAC;IAEhC,MAAM,CAAC,GAAG,CACR,IAAI,GAAG,CAAC,aAAa,CAAC,sBAAsB,CAAC,CAAC,QAAQ,EACtD,IAAA,mCAAoB,EAAC,EAAE,QAAQ,EAAE,OAAO,CAAC,QAAQ,EAAE,GAAG,OAAO,CAAC,oBAAoB,EAAE,CAAC,CACtF,CAAC;IAEF,MAAM,CAAC,GAAG,CACR,IAAI,GAAG,CAAC,aAAa,CAAC,cAAc,CAAC,CAAC,QAAQ,EAC9C,IAAA,uBAAY,EAAC,EAAE,QAAQ,EAAE,OAAO,CAAC,QAAQ,EAAE,GAAG,OAAO,CAAC,YAAY,EAAE,CAAC,CACtE,CAAC;IAEF,MAAM,CAAC,GAAG,CAAC,qBAAqB,CAAC;QAC/B,aAAa;QACb,mFAAmF;QACnF,iBAAiB,EAAE,IAAI,GAAG,CAAC,aAAa,CAAC,MAAM,CAAC;QAChD,uBAAuB,EAAE,OAAO,CAAC,uBAAuB;QACxD,eAAe,EAAE,OAAO,CAAC,eAAe;QACxC,YAAY,EAAE,OAAO,CAAC,YAAY;KACnC,CAAC,CAAC,CAAC;IAEJ,IAAI,aAAa,CAAC,qBAAqB,EAAE,CAAC;QACxC,MAAM,CAAC,GAAG,CACR,IAAI,GAAG,CAAC,aAAa,CAAC,qBAAqB,CAAC,CAAC,QAAQ,EACrD,IAAA,uCAAyB,EAAC;YACxB,YAAY,EAAE,OAAO,CAAC,QAAQ,CAAC,YAAY;YAC3C,GAAG,OAAO,CAAC,yBAAyB;SACrC,CAAC,CACH,CAAC;IACJ,CAAC;IAED,IAAI,aAAa,CAAC,mBAAmB,EAAE,CAAC;QACtC,MAAM,CAAC,GAAG,CACR,IAAI,GAAG,CAAC,aAAa,CAAC,mBAAmB,CAAC,CAAC,QAAQ,EACnD,IAAA,6BAAiB,EAAC,EAAE,QAAQ,EAAE,OAAO,CAAC,QAAQ,EAAE,GAAG,OAAO,CAAC,iBAAiB,EAAE,CAAC,CAChF,CAAC;IACJ,CAAC;IAED,OAAO,MAAM,CAAC;AAChB,CAAC;AA8BD,SAAgB,qBAAqB,CAAC,OAA4B;;IAChE,cAAc,CAAC,IAAI,GAAG,CAAC,OAAO,CAAC,aAAa,CAAC,MAAM,CAAC,CAAC,CAAC;IAEtD,MAAM,MAAM,GAAG,iBAAO,CAAC,MAAM,EAAE,CAAC;IAEhC,MAAM,yBAAyB,GAAmC;QAChE,QAAQ,EAAE,OAAO,CAAC,iBAAiB,CAAC,IAAI;QAExC,qBAAqB,EAAE;YACrB,OAAO,CAAC,aAAa,CAAC,MAAM;SAC7B;QAED,gBAAgB,EAAE,OAAO,CAAC,eAAe;QACzC,aAAa,EAAE,OAAO,CAAC,YAAY;QACnC,sBAAsB,EAAE,MAAA,OAAO,CAAC,uBAAuB,0CAAE,IAAI;KAC9D,CAAC;IAEF,MAAM,CAAC,GAAG,CAAC,uCAAuC,EAAE,IAAA,6BAAe,EAAC,yBAAyB,CAAC,CAAC,CAAC;IAEhG,8CAA8C;IAC9C,MAAM,CAAC,GAAG,CAAC,yCAAyC,EAAE,IAAA,6BAAe,EAAC,OAAO,CAAC,aAAa,CAAC,CAAC,CAAC;IAE9F,OAAO,MAAM,CAAC;AAChB,CAAC;AAED;;;;;;;;;;GAUG;AACH,SAAgB,oCAAoC,CAAC,SAAc;IACjE,OAAO,IAAI,GAAG,CAAC,uCAAuC,EAAE,SAAS,CAAC,CAAC,IAAI,CAAC;AAC1E,CAAC"}