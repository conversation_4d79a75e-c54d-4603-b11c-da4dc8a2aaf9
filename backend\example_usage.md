# 三峡水利调度Agent使用示例

## 配置完成确认

✅ **servers_config.json** 已更新，添加了三峡调度MCP服务器配置：
```json
"sanxiaScheduling": {
  "command": "python",
  "args": ["./Langgraph_Learning/MCP/sch_mcp.py"],
  "transport": "stdio"
}
```

✅ **agent_prompts.txt** 已更新，添加了三峡水利调度Agent的详细说明：
- 包含4个专业工具的完整描述
- 详细的参数说明和使用场景
- 与其他Agent的协作指导

## 用户交互示例

现在用户可以通过以下方式与Agent交互来使用水库调度功能：

### 1. 获取默认参数
```
用户: "获取三峡调度模型的默认参数配置"

Agent会调用: get_default_parameters()
返回: 完整的参数配置和说明
```

### 2. 查看可用数据
```
用户: "列出所有可用的洪水场景数据"

Agent会调用: list_available_scenarios()
返回: 所有可用年份和频率的列表
```

### 3. 验证数据可用性
```
用户: "验证1954年0.01频率的洪水数据是否可用"

Agent会调用: validate_scenario_data("1954", "0.01")
返回: 数据验证结果和详细信息
```

### 4. 执行调度计算
```
用户: "计算1954年0.01频率的洪水调度"

Agent会调用: sanxia_flood_routing([{"year":"1954","frequency":"0.01"}])
返回: 完整的调度计算结果
```

### 5. 自定义参数计算
```
用户: "计算1954年0.01频率的洪水调度，初始水位设为165米，上游最大动用设为80"

Agent会调用: sanxia_flood_routing(
  [{"year":"1954","frequency":"0.01"}], 
  init_water_level=165, 
  upstream_max=80
)
```

### 6. 批量场景计算
```
用户: "同时计算1954年的0.01和0.001两个频率的洪水调度"

Agent会调用: sanxia_flood_routing([
  {"year":"1954","frequency":"0.01"},
  {"year":"1954","frequency":"0.001"}
])
```

### 7. 结合数据分析
```
用户: "计算1954年0.01频率的调度结果，然后绘制水位变化图"

Agent会：
1. 调用 sanxia_flood_routing() 获取计算结果
2. 调用 fig_inter() 绘制水位变化图表
```

## Agent智能识别场景

Agent现在能够智能识别用户的水利工程需求：

- **关键词识别**: "三峡"、"调度"、"洪水"、"水位"、"库容"等
- **任务类型**: 水库调度、洪水计算、水位预测、库容分析
- **参数理解**: 自动解析年份、频率、水位等参数
- **工作流协调**: 与数据库、计算、可视化等其他Agent协作

## 完整工作流示例

```
用户: "我想分析1954年和1998年的特大洪水情况，计算0.01频率下的三峡调度方案，并对比两年的最高水位"

Agent执行流程:
1. 调用 validate_scenario_data() 验证两年数据可用性
2. 调用 sanxia_flood_routing() 计算两个场景
3. 调用 python_inter() 分析对比结果
4. 调用 fig_inter() 绘制对比图表
5. 向用户展示完整分析结果
```

## 启动测试

要测试新配置的功能，可以：

1. **启动Agent**:
   ```bash
   python client.py
   # 或
   python graph.py
   ```

2. **测试基本功能**:
   ```
   "获取三峡调度模型的默认参数"
   ```

3. **测试计算功能**:
   ```
   "计算1954年0.01频率的洪水调度"
   ```

4. **测试复杂场景**:
   ```
   "计算1954年0.01频率和0.001频率两个场景的调度结果，并分析差异"
   ```

## 注意事项

1. **数据依赖**: 确保 `flood_control/parameter/` 目录下有完整的数据文件
2. **环境配置**: 确保安装了所需的Python依赖包
3. **权限设置**: 确保Agent有访问文件系统的权限
4. **错误处理**: Agent会自动处理错误并提供友好的错误信息

现在三峡水利调度功能已完全集成到Agent系统中，用户可以通过自然语言与Agent交互来完成复杂的水利工程计算任务！
