"use client";

import React from "react";
import WordPreview from "@/components/word-preview";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";

export default function TestWordPage() {
  const handleSectionSelect = (section: any) => {
    console.log("Section selected:", section);
  };

  const handleDocumentLoad = (document: any) => {
    console.log("Document loaded:", document);
  };

  const handleStartEditing = (sectionId: string) => {
    console.log("Start editing section:", sectionId);
  };

  return (
    <div className="min-h-screen bg-gray-50 p-4">
      <div className="max-w-6xl mx-auto">
        <Card className="mb-6">
          <CardHeader>
            <CardTitle>Word编辑Agent测试页面</CardTitle>
          </CardHeader>
          <CardContent>
            <p className="text-gray-600">
              这是一个测试页面，用于验证Word文档上传和预览功能。
            </p>
          </CardContent>
        </Card>

        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          {/* 左侧：聊天区域（模拟） */}
          <Card>
            <CardHeader>
              <CardTitle>AI聊天界面</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="h-96 bg-gray-100 rounded-lg p-4 flex items-center justify-center">
                <p className="text-gray-500">聊天界面将在这里显示</p>
              </div>
            </CardContent>
          </Card>

          {/* 右侧：Word预览 */}
          <Card>
            <CardHeader>
              <CardTitle>Word文档预览</CardTitle>
            </CardHeader>
            <CardContent className="p-0">
              <WordPreview
                className="h-96"
                onSectionSelect={handleSectionSelect}
                onDocumentLoad={handleDocumentLoad}
                onStartEditing={handleStartEditing}
              />
            </CardContent>
          </Card>
        </div>

        {/* 测试说明 */}
        <Card className="mt-6">
          <CardHeader>
            <CardTitle>测试说明</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-2 text-sm">
              <p><strong>1. 文件上传测试：</strong></p>
              <ul className="list-disc list-inside ml-4 space-y-1">
                <li>点击右侧"选择文件"按钮，应该弹出文件选择器</li>
                <li>选择.docx或.doc文件，应该自动开始上传</li>
                <li>上传成功后应该显示成功状态</li>
              </ul>
              
              <p><strong>2. 文档解析测试：</strong></p>
              <ul className="list-disc list-inside ml-4 space-y-1">
                <li>上传成功后应该自动调用后端API进行文档分析</li>
                <li>分析完成后应该显示章节列表</li>
                <li>每个章节应该显示标题和字数统计</li>
              </ul>
              
              <p><strong>3. 章节选择测试：</strong></p>
              <ul className="list-disc list-inside ml-4 space-y-1">
                <li>点击章节应该能选中该章节</li>
                <li>选中后底部应该显示"开始编辑"按钮</li>
                <li>点击"开始编辑"应该有响应（弹出提示或调用API）</li>
              </ul>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
}
