#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
调试测试脚本
"""

import sys
import os
import traceback

print("开始调试测试...")

try:
    print("1. 测试导入模块...")
    import pandas as pd
    import numpy as np
    import json
    from scipy.interpolate import interp1d
    print("   模块导入成功")
    
    print("2. 测试配置文件读取...")
    current_dir = os.path.dirname(os.path.abspath(__file__))
    config_path = os.path.join(current_dir, 'parameter', 'input.json')
    print(f"   配置文件路径: {config_path}")
    print(f"   文件是否存在: {os.path.exists(config_path)}")
    
    if os.path.exists(config_path):
        with open(config_path, 'r', encoding='utf-8') as f:
            config = json.load(f)
        print("   配置文件读取成功")
        print(f"   配置内容: {config}")
    else:
        print("   配置文件不存在!")
        
    print("3. 测试数据文件...")
    if os.path.exists(config_path):
        flood_file = os.path.join(current_dir, config['data_path']['flood_file_path'])
        capacity_file = os.path.join(current_dir, config['data_path']['capacity_file_path'])
        drainage_file = os.path.join(current_dir, config['data_path']['drainage_file_path'])
        
        print(f"   洪水文件: {flood_file}, 存在: {os.path.exists(flood_file)}")
        print(f"   库容文件: {capacity_file}, 存在: {os.path.exists(capacity_file)}")
        print(f"   泄流文件: {drainage_file}, 存在: {os.path.exists(drainage_file)}")
        
    print("4. 测试主函数导入...")
    from sanxia_scheduling import perform_flood_routing
    print("   主函数导入成功")
    
    print("调试测试完成!")
    
except Exception as e:
    print(f"错误: {e}")
    traceback.print_exc()
