{"version": 3, "file": "index.js", "sources": ["../src/errors.ts", "../src/EventSource.ts"], "sourcesContent": ["/**\n * An extended version of the `Event` emitted by the `EventSource` object when an error occurs.\n * While the spec does not include any additional properties, we intentionally go beyond the spec\n * and provide some (minimal) additional information to aid in debugging.\n *\n * @public\n */\nexport class ErrorEvent extends Event {\n  /**\n   * HTTP status code, if this was triggered by an HTTP error\n   * Note: this is not part of the spec, but is included for better error handling.\n   *\n   * @public\n   */\n  public code?: number | undefined\n\n  /**\n   * Optional message attached to the error.\n   * Note: this is not part of the spec, but is included for better error handling.\n   *\n   * @public\n   */\n  public message?: string | undefined\n\n  /**\n   * Constructs a new `ErrorEvent` instance. This is typically not called directly,\n   * but rather emitted by the `EventSource` object when an error occurs.\n   *\n   * @param type - The type of the event (should be \"error\")\n   * @param errorEventInitDict - Optional properties to include in the error event\n   */\n  constructor(\n    type: string,\n    errorEventInitDict?: {message?: string | undefined; code?: number | undefined},\n  ) {\n    super(type)\n    this.code = errorEventInitDict?.code ?? undefined\n    this.message = errorEventInitDict?.message ?? undefined\n  }\n\n  /**\n   * Node.js \"hides\" the `message` and `code` properties of the `ErrorEvent` instance,\n   * when it is `console.log`'ed. This makes it harder to debug errors. To ease debugging,\n   * we explicitly include the properties in the `inspect` method.\n   *\n   * This is automatically called by Node.js when you `console.log` an instance of this class.\n   *\n   * @param _depth - The current depth\n   * @param options - The options passed to `util.inspect`\n   * @param inspect - The inspect function to use (prevents having to import it from `util`)\n   * @returns A string representation of the error\n   */\n  [Symbol.for('nodejs.util.inspect.custom')](\n    _depth: number,\n    options: {colors: boolean},\n    inspect: (obj: unknown, inspectOptions: {colors: boolean}) => string,\n  ): string {\n    return inspect(inspectableError(this), options)\n  }\n\n  /**\n   * Deno \"hides\" the `message` and `code` properties of the `ErrorEvent` instance,\n   * when it is `console.log`'ed. This makes it harder to debug errors. To ease debugging,\n   * we explicitly include the properties in the `inspect` method.\n   *\n   * This is automatically called by Deno when you `console.log` an instance of this class.\n   *\n   * @param inspect - The inspect function to use (prevents having to import it from `util`)\n   * @param options - The options passed to `Deno.inspect`\n   * @returns A string representation of the error\n   */\n  [Symbol.for('Deno.customInspect')](\n    inspect: (obj: unknown, inspectOptions: {colors: boolean}) => string,\n    options: {colors: boolean},\n  ): string {\n    return inspect(inspectableError(this), options)\n  }\n}\n\n/**\n * For environments where DOMException may not exist, we will use a SyntaxError instead.\n * While this isn't strictly according to spec, it is very close.\n *\n * @param message - The message to include in the error\n * @returns A `DOMException` or `SyntaxError` instance\n * @internal\n */\nexport function syntaxError(message: string): SyntaxError {\n  // If someone can figure out a way to make this work without depending on DOM/Node.js typings,\n  // and without casting to `any`, please send a PR 🙏\n\n  // eslint-disable-next-line @typescript-eslint/no-explicit-any\n  const DomException = (globalThis as any).DOMException\n  if (typeof DomException === 'function') {\n    return new DomException(message, 'SyntaxError')\n  }\n\n  return new SyntaxError(message)\n}\n\n/**\n * Flatten an error into a single error message string.\n * Unwraps nested errors and joins them with a comma.\n *\n * @param err - The error to flatten\n * @returns A string representation of the error\n * @internal\n */\nexport function flattenError(err: unknown): string {\n  if (!(err instanceof Error)) {\n    return `${err}`\n  }\n\n  if ('errors' in err && Array.isArray(err.errors)) {\n    return err.errors.map(flattenError).join(', ')\n  }\n\n  if ('cause' in err && err.cause instanceof Error) {\n    return `${err}: ${flattenError(err.cause)}`\n  }\n\n  return err.message\n}\n\n/**\n * Convert an `ErrorEvent` instance into a plain object for inspection.\n *\n * @param err - The `ErrorEvent` instance to inspect\n * @returns A plain object representation of the error\n * @internal\n */\nfunction inspectableError(err: ErrorEvent) {\n  return {\n    type: err.type,\n    message: err.message,\n    code: err.code,\n    defaultPrevented: err.defaultPrevented,\n    cancelable: err.cancelable,\n    timeStamp: err.timeStamp,\n  }\n}\n", "import {createParser, type EventSourceMessage, type EventSourceParser} from 'eventsource-parser'\n\nimport {ErrorEvent, flattenError, syntaxError} from './errors.js'\nimport type {\n  AddEventListenerOptions,\n  EventListenerOptions,\n  EventListenerOrEventListenerObject,\n  EventSourceEventMap,\n  EventSourceFetchInit,\n  EventSourceInit,\n  FetchLike,\n  FetchLikeResponse,\n} from './types.js'\n\n/**\n * An `EventSource` instance opens a persistent connection to an HTTP server, which sends events\n * in `text/event-stream` format. The connection remains open until closed by calling `.close()`.\n *\n * @public\n * @example\n * ```js\n * const eventSource = new EventSource('https://example.com/stream')\n * eventSource.addEventListener('error', (error) => {\n *   console.error(error)\n * })\n * eventSource.addEventListener('message', (event) => {\n *  console.log('Received message:', event.data)\n * })\n * ```\n */\nexport class EventSource extends EventTarget {\n  /**\n   * ReadyState representing an EventSource currently trying to connect\n   *\n   * @public\n   */\n  static CONNECTING = 0 as const\n\n  /**\n   * ReadyState representing an EventSource connection that is open (eg connected)\n   *\n   * @public\n   */\n  static OPEN = 1 as const\n\n  /**\n   * ReadyState representing an EventSource connection that is closed (eg disconnected)\n   *\n   * @public\n   */\n  static CLOSED = 2 as const\n\n  /**\n   * ReadyState representing an EventSource currently trying to connect\n   *\n   * @public\n   */\n  readonly CONNECTING = 0 as const\n\n  /**\n   * ReadyState representing an EventSource connection that is open (eg connected)\n   *\n   * @public\n   */\n  readonly OPEN = 1 as const\n\n  /**\n   * ReadyState representing an EventSource connection that is closed (eg disconnected)\n   *\n   * @public\n   */\n  readonly CLOSED = 2 as const\n\n  /**\n   * Returns the state of this EventSource object's connection. It can have the values described below.\n   *\n   * [MDN Reference](https://developer.mozilla.org/docs/Web/API/EventSource/readyState)\n   *\n   * Note: typed as `number` instead of `0 | 1 | 2` for compatibility with the `EventSource` interface,\n   * defined in the TypeScript `dom` library.\n   *\n   * @public\n   */\n  public get readyState(): number {\n    return this.#readyState\n  }\n\n  /**\n   * Returns the URL providing the event stream.\n   *\n   * [MDN Reference](https://developer.mozilla.org/docs/Web/API/EventSource/url)\n   *\n   * @public\n   */\n  public get url(): string {\n    return this.#url.href\n  }\n\n  /**\n   * Returns true if the credentials mode for connection requests to the URL providing the event stream is set to \"include\", and false otherwise.\n   *\n   * [MDN Reference](https://developer.mozilla.org/docs/Web/API/EventSource/withCredentials)\n   */\n  public get withCredentials(): boolean {\n    return this.#withCredentials\n  }\n\n  /** [MDN Reference](https://developer.mozilla.org/docs/Web/API/EventSource/error_event) */\n  public get onerror(): ((ev: ErrorEvent) => unknown) | null {\n    return this.#onError\n  }\n  public set onerror(value: ((ev: ErrorEvent) => unknown) | null) {\n    this.#onError = value\n  }\n\n  /** [MDN Reference](https://developer.mozilla.org/docs/Web/API/EventSource/message_event) */\n  public get onmessage(): ((ev: MessageEvent) => unknown) | null {\n    return this.#onMessage\n  }\n  public set onmessage(value: ((ev: MessageEvent) => unknown) | null) {\n    this.#onMessage = value\n  }\n\n  /** [MDN Reference](https://developer.mozilla.org/docs/Web/API/EventSource/open_event) */\n  public get onopen(): ((ev: Event) => unknown) | null {\n    return this.#onOpen\n  }\n  public set onopen(value: ((ev: Event) => unknown) | null) {\n    this.#onOpen = value\n  }\n\n  override addEventListener<K extends keyof EventSourceEventMap>(\n    type: K,\n    listener: (this: EventSource, ev: EventSourceEventMap[K]) => unknown,\n    options?: boolean | AddEventListenerOptions,\n  ): void\n  override addEventListener(\n    type: string,\n    listener: (this: EventSource, event: MessageEvent) => unknown,\n    options?: boolean | AddEventListenerOptions,\n  ): void\n  override addEventListener(\n    type: string,\n    listener: EventListenerOrEventListenerObject,\n    options?: boolean | AddEventListenerOptions,\n  ): void\n  override addEventListener(\n    type: string,\n    listener:\n      | ((this: EventSource, event: MessageEvent) => unknown)\n      | EventListenerOrEventListenerObject,\n    options?: boolean | AddEventListenerOptions,\n  ): void {\n    const listen = listener as (this: EventSource, event: Event) => unknown\n    super.addEventListener(type, listen, options)\n  }\n\n  override removeEventListener<K extends keyof EventSourceEventMap>(\n    type: K,\n    listener: (this: EventSource, ev: EventSourceEventMap[K]) => unknown,\n    options?: boolean | EventListenerOptions,\n  ): void\n  override removeEventListener(\n    type: string,\n    listener: (this: EventSource, event: MessageEvent) => unknown,\n    options?: boolean | EventListenerOptions,\n  ): void\n  override removeEventListener(\n    type: string,\n    listener: EventListenerOrEventListenerObject,\n    options?: boolean | EventListenerOptions,\n  ): void\n  override removeEventListener(\n    type: string,\n    listener:\n      | ((this: EventSource, event: MessageEvent) => unknown)\n      | EventListenerOrEventListenerObject,\n    options?: boolean | EventListenerOptions,\n  ): void {\n    const listen = listener as (this: EventSource, event: Event) => unknown\n    super.removeEventListener(type, listen, options)\n  }\n\n  constructor(url: string | URL, eventSourceInitDict?: EventSourceInit) {\n    super()\n\n    try {\n      if (url instanceof URL) {\n        this.#url = url\n      } else if (typeof url === 'string') {\n        this.#url = new URL(url, getBaseURL())\n      } else {\n        throw new Error('Invalid URL')\n      }\n    } catch (err) {\n      throw syntaxError('An invalid or illegal string was specified')\n    }\n\n    this.#parser = createParser({\n      onEvent: this.#onEvent,\n      onRetry: this.#onRetryChange,\n    })\n\n    this.#readyState = this.CONNECTING\n    this.#reconnectInterval = 3000\n    this.#fetch = eventSourceInitDict?.fetch ?? globalThis.fetch\n    this.#withCredentials = eventSourceInitDict?.withCredentials ?? false\n\n    this.#connect()\n  }\n\n  /**\n   * Aborts any instances of the fetch algorithm started for this EventSource object, and sets the readyState attribute to CLOSED.\n   *\n   * [MDN Reference](https://developer.mozilla.org/docs/Web/API/EventSource/close)\n   *\n   * @public\n   */\n  close(): void {\n    if (this.#reconnectTimer) clearTimeout(this.#reconnectTimer)\n    if (this.#readyState === this.CLOSED) return\n    if (this.#controller) this.#controller.abort()\n    this.#readyState = this.CLOSED\n    this.#controller = undefined\n  }\n\n  // PRIVATES FOLLOW\n\n  /**\n   * Current connection state\n   *\n   * @internal\n   */\n  #readyState: number\n\n  /**\n   * Original URL used to connect.\n   *\n   * Note that this will stay the same even after a redirect.\n   *\n   * @internal\n   */\n  #url: URL\n\n  /**\n   * The destination URL after a redirect. Is reset on reconnection.\n   *\n   * @internal\n   */\n  #redirectUrl: URL | undefined\n\n  /**\n   * Whether to include credentials in the request\n   *\n   * @internal\n   */\n  #withCredentials: boolean\n\n  /**\n   * The fetch implementation to use\n   *\n   * @internal\n   */\n  #fetch: FetchLike\n\n  /**\n   * The reconnection time in milliseconds\n   *\n   * @internal\n   */\n  #reconnectInterval: number\n\n  /**\n   * Reference to an ongoing reconnect attempt, if any\n   *\n   * @internal\n   */\n  #reconnectTimer: ReturnType<typeof setTimeout> | undefined\n\n  /**\n   * The last event ID seen by the EventSource, which will be sent as `Last-Event-ID` in the\n   * request headers on a reconnection attempt.\n   *\n   * @internal\n   */\n  #lastEventId: string | null = null\n\n  /**\n   * The AbortController instance used to abort the fetch request\n   *\n   * @internal\n   */\n  #controller: AbortController | undefined\n\n  /**\n   * Instance of an EventSource parser (`eventsource-parser` npm module)\n   *\n   * @internal\n   */\n  #parser: EventSourceParser\n\n  /**\n   * Holds the current error handler, attached through `onerror` property directly.\n   * Note that `addEventListener('error', …)` will not be stored here.\n   *\n   * @internal\n   */\n  #onError: ((ev: ErrorEvent) => unknown) | null = null\n\n  /**\n   * Holds the current message handler, attached through `onmessage` property directly.\n   * Note that `addEventListener('message', …)` will not be stored here.\n   *\n   * @internal\n   */\n  #onMessage: ((ev: MessageEvent) => unknown) | null = null\n\n  /**\n   * Holds the current open handler, attached through `onopen` property directly.\n   * Note that `addEventListener('open', …)` will not be stored here.\n   *\n   * @internal\n   */\n  #onOpen: ((ev: Event) => unknown) | null = null\n\n  /**\n   * Connect to the given URL and start receiving events\n   *\n   * @internal\n   */\n  #connect() {\n    this.#readyState = this.CONNECTING\n    this.#controller = new AbortController()\n\n    // Browser tests are failing if we directly call `this.#fetch()`, thus the indirection.\n    const fetch = this.#fetch\n    fetch(this.#url, this.#getRequestOptions())\n      .then(this.#onFetchResponse)\n      .catch(this.#onFetchError)\n  }\n\n  /**\n   * Handles the fetch response\n   *\n   * @param response - The Fetch(ish) response\n   * @internal\n   */\n  #onFetchResponse = async (response: FetchLikeResponse) => {\n    this.#parser.reset()\n\n    const {body, redirected, status, headers} = response\n\n    // [spec] a client can be told to stop reconnecting using the HTTP 204 No Content response code.\n    if (status === 204) {\n      // We still need to emit an error event - this mirrors the browser behavior,\n      // and without it there is no way to tell the user that the connection was closed.\n      this.#failConnection('Server sent HTTP 204, not reconnecting', 204)\n      this.close()\n      return\n    }\n\n    // [spec] …Event stream requests can be redirected using HTTP 301 and 307 redirects as with\n    // [spec] normal HTTP requests.\n    // Spec does not say anything about other redirect codes (302, 308), but this seems an\n    // unintended omission, rather than a feature. Browsers will happily redirect on other 3xxs's.\n    if (redirected) {\n      this.#redirectUrl = new URL(response.url)\n    } else {\n      this.#redirectUrl = undefined\n    }\n\n    // [spec] if res's status is not 200, …, then fail the connection.\n    if (status !== 200) {\n      this.#failConnection(`Non-200 status code (${status})`, status)\n      return\n    }\n\n    // [spec] …or if res's `Content-Type` is not `text/event-stream`, then fail the connection.\n    const contentType = headers.get('content-type') || ''\n    if (!contentType.startsWith('text/event-stream')) {\n      this.#failConnection('Invalid content type, expected \"text/event-stream\"', status)\n      return\n    }\n\n    // [spec] …if the readyState attribute is set to a value other than CLOSED…\n    if (this.#readyState === this.CLOSED) {\n      return\n    }\n\n    // [spec] …sets the readyState attribute to OPEN and fires an event\n    // [spec] …named open at the EventSource object.\n    this.#readyState = this.OPEN\n\n    const openEvent = new Event('open')\n    this.#onOpen?.(openEvent)\n    this.dispatchEvent(openEvent)\n\n    // Ensure that the response stream is a web stream\n    if (typeof body !== 'object' || !body || !('getReader' in body)) {\n      this.#failConnection('Invalid response body, expected a web ReadableStream', status)\n      this.close() // This should only happen if `fetch` provided is \"faulty\" - don't reconnect\n      return\n    }\n\n    const decoder = new TextDecoder()\n\n    const reader = body.getReader()\n    let open = true\n\n    do {\n      const {done, value} = await reader.read()\n      if (value) {\n        this.#parser.feed(decoder.decode(value, {stream: !done}))\n      }\n\n      if (!done) {\n        continue\n      }\n\n      open = false\n      this.#parser.reset()\n\n      this.#scheduleReconnect()\n    } while (open)\n  }\n\n  /**\n   * Handles rejected requests for the EventSource endpoint\n   *\n   * @param err - The error from `fetch()`\n   * @internal\n   */\n  #onFetchError = (err: Error & {type?: string}) => {\n    this.#controller = undefined\n\n    // We expect abort errors when the user manually calls `close()` - ignore those\n    if (err.name === 'AbortError' || err.type === 'aborted') {\n      return\n    }\n\n    this.#scheduleReconnect(flattenError(err))\n  }\n\n  /**\n   * Get request options for the `fetch()` request\n   *\n   * @returns The request options\n   * @internal\n   */\n  #getRequestOptions(): EventSourceFetchInit {\n    const lastEvent = this.#lastEventId ? {'Last-Event-ID': this.#lastEventId} : undefined\n\n    const init: EventSourceFetchInit = {\n      // [spec] Let `corsAttributeState` be `Anonymous`…\n      // [spec] …will have their mode set to \"cors\"…\n      mode: 'cors',\n      redirect: 'follow',\n      headers: {Accept: 'text/event-stream', ...lastEvent},\n      cache: 'no-store',\n      signal: this.#controller?.signal,\n    }\n\n    // Some environments crash if attempting to set `credentials` where it is not supported,\n    // eg on Cloudflare Workers. To avoid this, we only set it in browser-like environments.\n    if ('window' in globalThis) {\n      // [spec] …and their credentials mode set to \"same-origin\"\n      // [spec] …if the `withCredentials` attribute is `true`, set the credentials mode to \"include\"…\n      init.credentials = this.withCredentials ? 'include' : 'same-origin'\n    }\n\n    return init\n  }\n\n  /**\n   * Called by EventSourceParser instance when an event has successfully been parsed\n   * and is ready to be processed.\n   *\n   * @param event - The parsed event\n   * @internal\n   */\n  #onEvent = (event: EventSourceMessage) => {\n    if (typeof event.id === 'string') {\n      this.#lastEventId = event.id\n    }\n\n    const messageEvent = new MessageEvent(event.event || 'message', {\n      data: event.data,\n      origin: this.#redirectUrl ? this.#redirectUrl.origin : this.#url.origin,\n      lastEventId: event.id || '',\n    })\n\n    // The `onmessage` property of the EventSource instance only triggers on messages without an\n    // `event` field, or ones that explicitly set `message`.\n    if (this.#onMessage && (!event.event || event.event === 'message')) {\n      this.#onMessage(messageEvent)\n    }\n\n    this.dispatchEvent(messageEvent)\n  }\n\n  /**\n   * Called by EventSourceParser instance when a new reconnection interval is received\n   * from the EventSource endpoint.\n   *\n   * @param value - The new reconnection interval in milliseconds\n   * @internal\n   */\n  #onRetryChange = (value: number) => {\n    this.#reconnectInterval = value\n  }\n\n  /**\n   * Handles the process referred to in the EventSource specification as \"failing a connection\".\n   *\n   * @param error - The error causing the connection to fail\n   * @param code - The HTTP status code, if available\n   * @internal\n   */\n  #failConnection(message?: string, code?: number) {\n    // [spec] …if the readyState attribute is set to a value other than CLOSED,\n    // [spec] sets the readyState attribute to CLOSED…\n    if (this.#readyState !== this.CLOSED) {\n      this.#readyState = this.CLOSED\n    }\n\n    // [spec] …and fires an event named `error` at the `EventSource` object.\n    // [spec] Once the user agent has failed the connection, it does not attempt to reconnect.\n    // [spec] > Implementations are especially encouraged to report detailed information\n    // [spec] > to their development consoles whenever an error event is fired, since little\n    // [spec] > to no information can be made available in the events themselves.\n    // Printing to console is not very programatically helpful, though, so we emit a custom event.\n    const errorEvent = new ErrorEvent('error', {code, message})\n\n    this.#onError?.(errorEvent)\n    this.dispatchEvent(errorEvent)\n  }\n\n  /**\n   * Schedules a reconnection attempt against the EventSource endpoint.\n   *\n   * @param message - The error causing the connection to fail\n   * @param code - The HTTP status code, if available\n   * @internal\n   */\n  #scheduleReconnect(message?: string, code?: number) {\n    // [spec] If the readyState attribute is set to CLOSED, abort the task.\n    if (this.#readyState === this.CLOSED) {\n      return\n    }\n\n    // [spec] Set the readyState attribute to CONNECTING.\n    this.#readyState = this.CONNECTING\n\n    // [spec] Fire an event named `error` at the EventSource object.\n    const errorEvent = new ErrorEvent('error', {code, message})\n    this.#onError?.(errorEvent)\n    this.dispatchEvent(errorEvent)\n\n    // [spec] Wait a delay equal to the reconnection time of the event source.\n    this.#reconnectTimer = setTimeout(this.#reconnect, this.#reconnectInterval)\n  }\n\n  /**\n   * Reconnects to the EventSource endpoint after a disconnect/failure\n   *\n   * @internal\n   */\n  #reconnect = () => {\n    this.#reconnectTimer = undefined\n\n    // [spec] If the EventSource's readyState attribute is not set to CONNECTING, then return.\n    if (this.#readyState !== this.CONNECTING) {\n      return\n    }\n\n    this.#connect()\n  }\n}\n\n/**\n * According to spec, when constructing a URL:\n * > 1. Let baseURL be environment's base URL, if environment is a Document object\n * > 2. Return the result of applying the URL parser to url, with baseURL.\n *\n * Thus we should use `document.baseURI` if available, since it can be set through a base tag.\n *\n * @returns The base URL, if available - otherwise `undefined`\n * @internal\n */\nfunction getBaseURL(): string | undefined {\n  // eslint-disable-next-line @typescript-eslint/no-explicit-any\n  const doc = 'document' in globalThis ? (globalThis as any).document : undefined\n  return doc && typeof doc === 'object' && 'baseURI' in doc && typeof doc.baseURI === 'string'\n    ? doc.baseURI\n    : undefined\n}\n"], "names": ["_a"], "mappings": ";AAOO,MAAM,mBAAmB,MAAM;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAwBpC,YACE,MACA,oBACA;AAlCJ,QAAA,IAAA;AAmCI,UAAM,IAAI,GACV,KAAK,QAAO,KAAoB,sBAAA,OAAA,SAAA,mBAAA,SAApB,OAA4B,KAAA,QACxC,KAAK,WAAU,KAAoB,sBAAA,OAAA,SAAA,mBAAA,YAApB,OAA+B,KAAA;AAAA,EAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAehD,CAAC,OAAO,IAAI,4BAA4B,CAAC,EACvC,QACA,SACA,SACQ;AACR,WAAO,QAAQ,iBAAiB,IAAI,GAAG,OAAO;AAAA,EAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAchD,CAAC,OAAO,IAAI,oBAAoB,CAAC,EAC/B,SACA,SACQ;AACR,WAAO,QAAQ,iBAAiB,IAAI,GAAG,OAAO;AAAA,EAAA;AAElD;AAUO,SAAS,YAAY,SAA8B;AAKxD,QAAM,eAAgB,WAAmB;AACrC,SAAA,OAAO,gBAAiB,aACnB,IAAI,aAAa,SAAS,aAAa,IAGzC,IAAI,YAAY,OAAO;AAChC;AAUO,SAAS,aAAa,KAAsB;AACjD,SAAM,eAAe,QAIjB,YAAY,OAAO,MAAM,QAAQ,IAAI,MAAM,IACtC,IAAI,OAAO,IAAI,YAAY,EAAE,KAAK,IAAI,IAG3C,WAAW,OAAO,IAAI,iBAAiB,QAClC,GAAG,GAAG,KAAK,aAAa,IAAI,KAAK,CAAC,KAGpC,IAAI,UAXF,GAAG,GAAG;AAYjB;AASA,SAAS,iBAAiB,KAAiB;AAClC,SAAA;AAAA,IACL,MAAM,IAAI;AAAA,IACV,SAAS,IAAI;AAAA,IACb,MAAM,IAAI;AAAA,IACV,kBAAkB,IAAI;AAAA,IACtB,YAAY,IAAI;AAAA,IAChB,WAAW,IAAI;AAAA,EACjB;AACF;;;0pBC5IA,aAAA,MAAA,cAAA,kBAAA,QAAA,oBAAA,iBAAA,cAAA,aAAA,SAAA,UAAA,YAAA,SAAA,wBAAA,YAAA,kBAAA,eAAA,sBAAA,UAAA,gBAAA,mBAAA,sBAAA;AA8BO,MAAM,oBAAoB,YAAY;AAAA,EAyJ3C,YAAY,KAAmB,qBAAuC;AAvLxE,QAAA,IAAA;AAwLU,UAAA,GA1JH,aAAA,MAAA,sBAAA,GA2BL,KAAS,aAAa,GAOtB,KAAS,OAAO,GAOhB,KAAS,SAAS,GAkKlB,aAAA,MAAA,WAAA,GASA,aAAA,MAAA,IAAA,GAOA,aAAA,MAAA,YAAA,GAOA,aAAA,MAAA,gBAAA,GAOA,aAAA,MAAA,MAAA,GAOA,aAAA,MAAA,kBAAA,GAOA,aAAA,MAAA,eAAA,GAQ8B,aAAA,MAAA,cAAA,IAAA,GAO9B,aAAA,MAAA,WAAA,GAOA,aAAA,MAAA,OAAA,GAQiD,aAAA,MAAA,UAAA,IAAA,GAQI,aAAA,MAAA,YAAA,IAAA,GAQV,aAAA,MAAA,SAAA,IAAA,GAwB3C,aAAA,MAAA,kBAAmB,OAAO,aAAgC;AA3V5D,UAAAA;AA4VI,mBAAA,MAAK,SAAQ,MAAM;AAEnB,YAAM,EAAC,MAAM,YAAY,QAAQ,QAAW,IAAA;AAG5C,UAAI,WAAW,KAAK;AAGlB,wBAAA,MAAK,wBAAL,iBAAA,EAAA,KAAA,MAAqB,0CAA0C,GAAA,GAC/D,KAAK,MAAM;AACX;AAAA,MAAA;AAcF,UAPI,aACF,aAAA,MAAK,cAAe,IAAI,IAAI,SAAS,GAAG,CAAA,IAExC,aAAK,MAAA,cAAe,MAIlB,GAAA,WAAW,KAAK;AAClB,wBAAA,MAAK,wBAAL,iBAAA,EAAA,KAAA,MAAqB,wBAAwB,MAAM,KAAK,MAAA;AACxD;AAAA,MAAA;AAKE,UAAA,EADgB,QAAQ,IAAI,cAAc,KAAK,IAClC,WAAW,mBAAmB,GAAG;AAC3C,wBAAA,MAAA,wBAAA,iBAAA,EAAL,WAAqB,sDAAsD,MAAA;AAC3E;AAAA,MAAA;AAIE,UAAA,aAAA,MAAK,iBAAgB,KAAK;AAC5B;AAKF,mBAAA,MAAK,aAAc,KAAK,IAAA;AAElB,YAAA,YAAY,IAAI,MAAM,MAAM;AAKlC,WAJAA,MAAK,aAAA,MAAA,OAAA,MAAL,QAAeA,IAAA,KAAA,MAAA,SAAA,GACf,KAAK,cAAc,SAAS,GAGxB,OAAO,QAAS,YAAY,CAAC,QAAQ,EAAE,eAAe,OAAO;AAC/D,wBAAA,MAAK,wBAAL,iBAAA,EAAA,KAAA,MAAqB,wDAAwD,MAAA,GAC7E,KAAK,MAAM;AACX;AAAA,MAAA;AAGF,YAAM,UAAU,IAAI,YAAA,GAEd,SAAS,KAAK,UAAU;AAC9B,UAAI,OAAO;AAER,SAAA;AACD,cAAM,EAAC,MAAM,MAAS,IAAA,MAAM,OAAO,KAAK;AACpC,iBACF,mBAAK,OAAQ,EAAA,KAAK,QAAQ,OAAO,OAAO,EAAC,QAAQ,CAAC,KAAA,CAAK,CAAC,GAGrD,SAIL,OAAO,IACP,mBAAK,OAAQ,EAAA,MAEb,GAAA,gBAAA,MAAK,wBAAL,oBAAA,EAAA,KAAA,IAAA;AAAA,MAAA,SACO;AAAA,IAAA,CACX,GAQA,aAAA,MAAA,eAAgB,CAAC,QAAiC;AAChD,mBAAK,MAAA,aAAc,MAGf,GAAA,EAAA,IAAI,SAAS,gBAAgB,IAAI,SAAS,cAI9C,gBAAA,MAAK,wBAAL,oBAAA,EAAA,KAAA,MAAwB,aAAa,GAAG,CAAA;AAAA,IAAA,CAC1C,GAuCA,aAAA,MAAA,UAAW,CAAC,UAA8B;AACpC,aAAO,MAAM,MAAO,YACtB,aAAA,MAAK,cAAe,MAAM,EAAA;AAG5B,YAAM,eAAe,IAAI,aAAa,MAAM,SAAS,WAAW;AAAA,QAC9D,MAAM,MAAM;AAAA,QACZ,QAAQ,aAAK,MAAA,YAAA,IAAe,mBAAK,YAAa,EAAA,SAAS,mBAAK,IAAK,EAAA;AAAA,QACjE,aAAa,MAAM,MAAM;AAAA,MAAA,CAC1B;AAIG,mBAAK,MAAA,UAAA,MAAe,CAAC,MAAM,SAAS,MAAM,UAAU,cACtD,aAAA,MAAK,UAAL,EAAA,KAAA,MAAgB,YAGlB,GAAA,KAAK,cAAc,YAAY;AAAA,IAAA,CACjC,GASA,aAAA,MAAA,gBAAiB,CAAC,UAAkB;AAClC,mBAAA,MAAK,oBAAqB,KAAA;AAAA,IAAA,CAC5B,GA0DA,aAAA,MAAA,YAAa,MAAM;AACjB,mBAAA,MAAK,iBAAkB,MAGnB,GAAA,aAAA,MAAK,iBAAgB,KAAK,cAI9B,sBAAK,wBAAL,UAAA,EAAA,KAAA,IAAA;AAAA,IAAA,CACF;AAtYM,QAAA;AACF,UAAI,eAAe;AACjB,qBAAA,MAAK,MAAO,GAAA;AAAA,eACH,OAAO,OAAQ;AACxB,qBAAA,MAAK,MAAO,IAAI,IAAI,KAAK,YAAY,CAAA;AAAA;AAE/B,cAAA,IAAI,MAAM,aAAa;AAAA,IAAA,QAEnB;AACZ,YAAM,YAAY,4CAA4C;AAAA,IAAA;AAGhE,iBAAA,MAAK,SAAU,aAAa;AAAA,MAC1B,SAAS,aAAK,MAAA,QAAA;AAAA,MACd,SAAS,aAAK,MAAA,cAAA;AAAA,IAAA,CACf,IAED,aAAK,MAAA,aAAc,KAAK,UACxB,GAAA,aAAA,MAAK,oBAAqB,GAAA,GAC1B,aAAK,MAAA,SAAS,gEAAqB,UAArB,OAAA,KAA8B,WAAW,KAAA,GACvD,aAAK,MAAA,mBAAmB,gEAAqB,oBAArB,OAAA,KAAwC,EAEhE,GAAA,gBAAA,MAAK,wBAAL,UAAA,EAAA,KAAA,IAAA;AAAA,EAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EA7HF,IAAW,aAAqB;AAC9B,WAAO,aAAK,MAAA,WAAA;AAAA,EAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAUd,IAAW,MAAc;AACvB,WAAO,mBAAK,IAAK,EAAA;AAAA,EAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQnB,IAAW,kBAA2B;AACpC,WAAO,aAAK,MAAA,gBAAA;AAAA,EAAA;AAAA;AAAA,EAId,IAAW,UAAgD;AACzD,WAAO,aAAK,MAAA,QAAA;AAAA,EAAA;AAAA,EAEd,IAAW,QAAQ,OAA6C;AAC9D,iBAAA,MAAK,UAAW,KAAA;AAAA,EAAA;AAAA;AAAA,EAIlB,IAAW,YAAoD;AAC7D,WAAO,aAAK,MAAA,UAAA;AAAA,EAAA;AAAA,EAEd,IAAW,UAAU,OAA+C;AAClE,iBAAA,MAAK,YAAa,KAAA;AAAA,EAAA;AAAA;AAAA,EAIpB,IAAW,SAA0C;AACnD,WAAO,aAAK,MAAA,OAAA;AAAA,EAAA;AAAA,EAEd,IAAW,OAAO,OAAwC;AACxD,iBAAA,MAAK,SAAU,KAAA;AAAA,EAAA;AAAA,EAkBR,iBACP,MACA,UAGA,SACM;AACN,UAAM,SAAS;AACT,UAAA,iBAAiB,MAAM,QAAQ,OAAO;AAAA,EAAA;AAAA,EAkBrC,oBACP,MACA,UAGA,SACM;AACN,UAAM,SAAS;AACT,UAAA,oBAAoB,MAAM,QAAQ,OAAO;AAAA,EAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAsCjD,QAAc;AACR,iBAAA,MAAK,oBAAiB,aAAa,aAAA,MAAK,gBAAe,GACvD,aAAA,MAAK,iBAAgB,KAAK,WAC1B,mBAAK,WAAa,KAAA,aAAA,MAAK,aAAY,MAAM,GAC7C,mBAAK,aAAc,KAAK,MACxB,GAAA,aAAA,MAAK,aAAc,MAAA;AAAA,EAAA;AAkWvB;AAxVE,cASA,oBAAA,QAAA,GAAA,OAAA,oBAAA,QAAA,GAOA,eAOA,oBAAA,QAAA,GAAA,mBAAA,oBAAA,WAOA,SAOA,oBAAA,QAAA,GAAA,qBAAA,oBAAA,QAAA,GAOA,kBAQA,oBAAA,QAAA,GAAA,eAAA,oBAAA,QAAA,GAOA,cAOA,oBAAA,QAAA,GAAA,UAAA,oBAAA,QAAA,GAQA,WAQA,oBAAA,WAAA,aAAA,oBAAA,WAQA,UArSK,oBAAA,QAAA,GAAA,yBAAA,oBAAA,QAAA;AAAA;AAAA;AAAA;AAAA;AA4SL,aAAQ,WAAG;AACJ,eAAA,MAAA,aAAc,KAAK,UACxB,GAAA,aAAA,MAAK,aAAc,IAAI,gBAAA,CAGT,GAAA,aAAA,MAAK,MACb,EAAA,aAAA,MAAK,OAAM,gBAAK,MAAA,wBAAA,oBAAA,EAAL,UAAyB,EACvC,KAAK,mBAAK,gBAAgB,CAAA,EAC1B,MAAM,aAAA,MAAK,aAAa,CAAA;AAC7B,GAQA,mBAqFA,oBAAA,QAAA,GAAA,gBAAA,oBAAA,QAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAiBA,uBAAkB,WAAyB;AAjc7C,MAAA;AAocI,QAAM,OAA6B;AAAA;AAAA;AAAA,IAGjC,MAAM;AAAA,IACN,UAAU;AAAA,IACV,SAAS,EAAC,QAAQ,qBAAqB,GAPvB,aAAA,MAAK,YAAe,IAAA,EAAC,iBAAiB,aAAA,MAAK,YAAY,EAAA,IAAI,OAOxB;AAAA,IACnD,OAAO;AAAA,IACP,SAAQ,KAAK,aAAA,MAAA,WAAA,MAAL,OAAkB,SAAA,GAAA;AAAA,EAC5B;AAIA,SAAI,YAAY,eAGd,KAAK,cAAc,KAAK,kBAAkB,YAAY,gBAGjD;AACT,GASA,WA2BA,oBAAA,QAAA,GAAA,iBAAA,oBAAA,QAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAWA,oBAAe,SAAC,SAAkB,MAAe;AAtgBnD,MAAA;AAygBQ,qBAAK,WAAgB,MAAA,KAAK,UAC5B,aAAA,MAAK,aAAc,KAAK,MAAA;AAS1B,QAAM,aAAa,IAAI,WAAW,SAAS,EAAC,MAAM,SAAQ;AAE1D,GAAA,KAAA,aAAA,MAAK,QAAL,MAAA,QAAA,GAAA,KAAA,MAAgB,UAChB,GAAA,KAAK,cAAc,UAAU;AAC/B;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AASA,uBAAkB,SAAC,SAAkB,MAAe;AAhiBtD,MAAA;AAkiBQ,MAAA,aAAA,MAAK,iBAAgB,KAAK;AAC5B;AAIF,eAAA,MAAK,aAAc,KAAK,UAAA;AAGxB,QAAM,aAAa,IAAI,WAAW,SAAS,EAAC,MAAM,SAAQ;AAC1D,GAAA,KAAA,aAAA,MAAK,QAAL,MAAA,QAAA,GAAA,KAAA,MAAgB,UAChB,GAAA,KAAK,cAAc,UAAU,GAG7B,aAAA,MAAK,iBAAkB,WAAW,aAAK,MAAA,UAAA,GAAY,mBAAK,kBAAkB,CAAA,CAAA;AAC5E,GAOA,aAAA,oBAAA,QAAA;AAAA;AAAA;AAAA;AAAA;AAzhBW,YAMJ,aAAa;AAAA;AAAA;AAAA;AAAA;AANT,YAaJ,OAAO;AAAA;AAAA;AAAA;AAAA;AAbH,YAoBJ,SAAS;AA2hBlB,SAAS,aAAiC;AAExC,QAAM,MAAM,cAAc,aAAc,WAAmB,WAAW;AAC/D,SAAA,OAAO,OAAO,OAAQ,YAAY,aAAa,OAAO,OAAO,IAAI,WAAY,WAChF,IAAI,UACJ;AACN;"}