#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
通用工具模块
包含中文转拼音、数据类型识别等工具函数
"""

import re
import pandas as pd
import numpy as np
from datetime import datetime
from typing import Dict, List, Any, Optional, Tuple
import logging

logger = logging.getLogger(__name__)

# 扩展的中文转拼音映射表
PINYIN_MAP = {
    # 数字
    '一': 'yi', '二': 'er', '三': 'san', '四': 'si', '五': 'wu', '六': 'liu', '七': 'qi', '八': 'ba', '九': 'jiu', '十': 'shi',
    '零': 'ling', '百': 'bai', '千': 'qian', '万': 'wan', '亿': 'yi',

    # 时间相关
    '年': 'nian', '月': 'yue', '日': 'ri', '期': 'qi', '时': 'shi', '间': 'jian', '分': 'fen', '秒': 'miao',
    '今': 'jin', '昨': 'zuo', '明': 'ming', '前': 'qian', '后': 'hou', '早': 'zao', '晚': 'wan',

    '峡': 'xia', '宜': 'yi', '枝': 'zhi', '螺': 'luo', '向': 'xiang', '家': 'jia', '坝': 'ba',
    # 数据相关
    '数': 'shu', '据': 'ju', '表': 'biao', '库': 'ku', '信': 'xin', '息': 'xi', '记': 'ji', '录': 'lu',
    '统': 'tong', '计': 'ji', '析': 'xi', '报': 'bao', '告': 'gao', '结': 'jie', '果': 'guo',

    # 水文相关
    '水': 'shui', '位': 'wei', '流': 'liu', '量': 'liang', '河': 'he', '湖': 'hu', '海': 'hai', '江': 'jiang',
    '溪': 'xi', '泉': 'quan', '井': 'jing', '池': 'chi', '塘': 'tang', '坝': 'ba', '堤': 'di',

    # 气象相关
    '温': 'wen', '度': 'du', '湿': 'shi', '压': 'ya', '力': 'li', '风': 'feng', '速': 'su', '雨': 'yu',
    '雪': 'xue', '雾': 'wu', '霜': 'shuang', '露': 'lu', '云': 'yun', '晴': 'qing', '阴': 'yin',

    # 地理相关
    '地': 'di', '区': 'qu', '省': 'sheng', '市': 'shi', '县': 'xian', '镇': 'zhen', '村': 'cun',
    '山': 'shan', '峰': 'feng', '岭': 'ling', '坡': 'po', '谷': 'gu', '平': 'ping', '原': 'yuan',
    '东': 'dong', '南': 'nan', '西': 'xi', '北': 'bei', '中': 'zhong', '央': 'yang',

    # 监测相关
    '监': 'jian', '测': 'ce', '控': 'kong', '制': 'zhi', '系': 'xi', '统': 'tong', '站': 'zhan', '点': 'dian',
    '设': 'she', '备': 'bei', '仪': 'yi', '器': 'qi', '传': 'chuan', '感': 'gan', '探': 'tan', '测': 'ce',

    # 管理相关
    '管': 'guan', '理': 'li', '运': 'yun', '营': 'ying', '维': 'wei', '护': 'hu', '保': 'bao', '养': 'yang',
    '检': 'jian', '查': 'cha', '修': 'xiu', '复': 'fu', '更': 'geng', '换': 'huan', '升': 'sheng', '级': 'ji',

    # 方位相关
    '上': 'shang', '下': 'xia', '左': 'zuo', '右': 'you', '内': 'nei', '外': 'wai',
    '顶': 'ding', '底': 'di', '边': 'bian', '角': 'jiao', '侧': 'ce', '端': 'duan',

    # 状态相关
    '正': 'zheng', '常': 'chang', '异': 'yi', '常': 'chang', '故': 'gu', '障': 'zhang', '错': 'cuo', '误': 'wu',
    '好': 'hao', '坏': 'huai', '新': 'xin', '旧': 'jiu', '开': 'kai', '关': 'guan', '启': 'qi', '停': 'ting',

    # 数量相关
    '多': 'duo', '少': 'shao', '大': 'da', '小': 'xiao', '高': 'gao', '低': 'di', '长': 'chang', '短': 'duan',
    '宽': 'kuan', '窄': 'zhai', '深': 'shen', '浅': 'qian', '厚': 'hou', '薄': 'bao', '重': 'zhong', '轻': 'qing',

    # 逻辑相关
    '是': 'shi', '否': 'fou', '有': 'you', '无': 'wu', '能': 'neng', '会': 'hui', '可': 'ke', '不': 'bu',
    '真': 'zhen', '假': 'jia', '实': 'shi', '虚': 'xu', '全': 'quan', '空': 'kong', '满': 'man', '缺': 'que',

    # 动作相关
    '增': 'zeng', '减': 'jian', '加': 'jia', '减': 'jian', '乘': 'cheng', '除': 'chu', '等': 'deng', '于': 'yu',
    '输': 'shu', '入': 'ru', '输': 'shu', '出': 'chu', '导': 'dao', '入': 'ru', '导': 'dao', '出': 'chu',

    # 业务相关
    '销': 'xiao', '售': 'shou', '购': 'gou', '买': 'mai', '客': 'ke', '户': 'hu', '产': 'chan', '品': 'pin',
    '订': 'ding', '单': 'dan', '库': 'ku', '存': 'cun', '价': 'jia', '格': 'ge', '金': 'jin', '额': 'e',
    '收': 'shou', '支': 'zhi', '成': 'cheng', '本': 'ben', '利': 'li', '润': 'run', '税': 'shui', '费': 'fei',

    # 其他常用字
    '总': 'zong', '合': 'he', '平': 'ping', '均': 'jun', '最': 'zui', '极': 'ji', '超': 'chao', '过': 'guo',
    '达': 'da', '到': 'dao', '完': 'wan', '成': 'cheng', '结': 'jie', '束': 'shu', '开': 'kai', '始': 'shi',
    '继': 'ji', '续': 'xu', '暂': 'zan', '停': 'ting', '终': 'zhong', '止': 'zhi', '结': 'jie', '束': 'shu'

}

# 尝试导入pypinyin库，如果没有安装则使用内置映射表
try:
    from pypinyin import lazy_pinyin, Style
    PYPINYIN_AVAILABLE = True
except ImportError:
    PYPINYIN_AVAILABLE = False

def chinese_to_pinyin(text) -> str:
    """
    将中文字符串转换为拼音
    优先使用pypinyin库，如果不可用则使用内置映射表

    Args:
        text: 输入的文本（可能是字符串、数字等）

    Returns:
        转换后的拼音字符串
    """
    if not text:
        return 'unknown_table'

    # 转换为字符串
    text = str(text)

    if PYPINYIN_AVAILABLE:
        # 使用pypinyin库进行转换
        try:
            pinyin_list = lazy_pinyin(text, style=Style.NORMAL)
            result = []
            for py in pinyin_list:
                # 确保py是字符串
                py_str = str(py)
                # 只保留字母和数字
                clean_py = re.sub(r'[^a-zA-Z0-9]', '', py_str)
                if clean_py:
                    result.append(clean_py.lower())
            return '_'.join(result) if result else 'unknown_table'
        except Exception as e:
            logger.warning(f"pypinyin转换失败，使用内置映射表: {e}")

    # 使用内置映射表
    result = []
    for char in text:
        if char in PINYIN_MAP:
            result.append(PINYIN_MAP[char])
        elif char.isalnum() or char == '_':
            result.append(char.lower())
        elif '\u4e00' <= char <= '\u9fff':  # 中文字符范围
            # 对于未映射的中文字符，使用Unicode编码
            result.append(f'zh_{ord(char)}')
        else:
            # 对于其他特殊字符，使用ASCII码转换
            result.append(f'char_{ord(char)}')

    return '_'.join(result) if result else 'unknown_table'

def sanitize_table_name(name: str) -> str:
    """
    清理表名，确保符合MySQL命名规范
    
    Args:
        name: 原始表名
        
    Returns:
        清理后的表名
    """
    # 转换中文为拼音
    name = chinese_to_pinyin(name)
    
    # 移除特殊字符，只保留字母、数字和下划线
    name = re.sub(r'[^a-zA-Z0-9_]', '_', name)
    
    # 确保以字母或下划线开头
    if name and name[0].isdigit():
        name = 'table_' + name
    
    # 限制长度（MySQL表名最大64字符）
    if len(name) > 60:
        name = name[:60]
    
    # 确保不为空
    if not name:
        name = 'unknown_table'
    
    return name.lower()

def detect_column_type(series: pd.Series, column_name: str) -> str:
    """
    检测pandas Series的数据类型，返回对应的MySQL数据类型
    
    Args:
        series: pandas Series对象
        column_name: 列名
        
    Returns:
        MySQL数据类型字符串
    """
    # 移除空值进行类型检测
    non_null_series = series.dropna()
    
    if len(non_null_series) == 0:
        return "VARCHAR(255)"
    
    # 检查是否为时间类型
    if is_datetime_column(column_name, non_null_series):
        return "DATETIME"
    
    # 检查数值类型
    if pd.api.types.is_numeric_dtype(non_null_series):
        # 检查是否为整数
        if pd.api.types.is_integer_dtype(non_null_series):
            max_val = non_null_series.max()
            min_val = non_null_series.min()
            
            if min_val >= -128 and max_val <= 127:
                return "TINYINT"
            elif min_val >= -32768 and max_val <= 32767:
                return "SMALLINT"
            elif min_val >= -2147483648 and max_val <= 2147483647:
                return "INT"
            else:
                return "BIGINT"
        else:
            # 浮点数类型
            return "FLOAT"
    
    # 检查布尔类型
    if pd.api.types.is_bool_dtype(non_null_series):
        return "BOOLEAN"
    
    # 默认为字符串类型
    max_length = non_null_series.astype(str).str.len().max()
    if max_length <= 255:
        return f"VARCHAR({max(max_length, 50)})"
    elif max_length <= 65535:
        return "TEXT"
    else:
        return "LONGTEXT"

def is_datetime_column(column_name: str, series: pd.Series) -> bool:
    """
    判断列是否为时间类型
    
    Args:
        column_name: 列名
        series: pandas Series对象
        
    Returns:
        是否为时间类型
    """
    from db_config import IMPORT_CONFIG
    
    # 检查列名是否包含时间相关关键词
    datetime_keywords = IMPORT_CONFIG['datetime_columns']
    column_name_lower = column_name.lower()
    
    for keyword in datetime_keywords:
        if keyword.lower() in column_name_lower:
            return True
    
    # 检查数据内容是否为时间格式
    if pd.api.types.is_datetime64_any_dtype(series):
        return True
    
    # 尝试解析前几个非空值
    sample_values = series.dropna().head(10)
    datetime_count = 0
    
    for value in sample_values:
        if isinstance(value, (datetime, pd.Timestamp)):
            datetime_count += 1
        elif isinstance(value, str):
            try:
                pd.to_datetime(value)
                datetime_count += 1
            except:
                pass
    
    # 如果超过一半的样本值可以解析为时间，则认为是时间列
    return datetime_count > len(sample_values) * 0.5

def create_table_sql(table_name: str, columns_info: Dict[str, str]) -> str:
    """
    生成创建表的SQL语句

    Args:
        table_name: 表名
        columns_info: 列信息字典，键为列名，值为数据类型

    Returns:
        CREATE TABLE SQL语句
    """
    columns = ["id INT AUTO_INCREMENT PRIMARY KEY"]

    for col_name, col_type in columns_info.items():
        # 清理列名，时间列统一命名为date
        if col_type == "DATETIME":
            clean_col_name = "date"
        else:
            clean_col_name = sanitize_column_name(col_name)
        columns.append(f"{clean_col_name} {col_type}")

    # 添加创建时间和更新时间
    columns.append("created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP")
    columns.append("updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP")

    sql = f"""
    CREATE TABLE IF NOT EXISTS {table_name} (
        {','.join(columns)}
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
    """

    return sql

def sanitize_column_name(name: str) -> str:
    """
    清理列名，确保符合MySQL命名规范

    Args:
        name: 原始列名

    Returns:
        清理后的列名
    """
    if not name:
        return 'unknown_col'

    # 转换为字符串（处理数字或其他类型）
    name = str(name)

    # 检查是否为纯数字或百分数
    if name.replace('.', '').replace('%', '').replace('-', '').isdigit():
        # 如果是数字或百分数，直接使用，但确保以字母开头
        clean_name = re.sub(r'[^a-zA-Z0-9_]', '_', name)
        if clean_name and clean_name[0].isdigit():
            clean_name = 'col_' + clean_name
        return clean_name.lower() if clean_name else 'unknown_col'

    # 对于中文或其他字符，转换为拼音
    try:
        name = chinese_to_pinyin(name)
    except Exception as e:
        logger.warning(f"列名转换失败: {e}, 使用原始名称")
        # 如果转换失败，直接清理特殊字符
        name = re.sub(r'[^a-zA-Z0-9_\u4e00-\u9fff]', '_', name)

    # 移除特殊字符，只保留字母、数字和下划线
    name = re.sub(r'[^a-zA-Z0-9_]', '_', name)

    # 确保以字母或下划线开头
    if name and name[0].isdigit():
        name = 'col_' + name

    # 限制长度
    if len(name) > 60:
        name = name[:60]

    # 确保不为空
    if not name:
        name = 'unknown_col'

    return name.lower()

def get_datetime_column(df: pd.DataFrame) -> Optional[str]:
    """
    获取DataFrame中的时间列名
    
    Args:
        df: pandas DataFrame
        
    Returns:
        时间列名，如果没有找到则返回None
    """
    for col in df.columns:
        if is_datetime_column(col, df[col]):
            return col
    return None

def prepare_data_for_insert(df: pd.DataFrame, columns_info: Dict[str, str]) -> List[Tuple]:
    """
    准备用于插入数据库的数据

    Args:
        df: pandas DataFrame
        columns_info: 列信息字典

    Returns:
        准备好的数据列表
    """
    data_list = []

    for _, row in df.iterrows():
        row_data = []
        for col_name in columns_info.keys():
            value = row[col_name]

            # 处理空值
            if pd.isna(value):
                row_data.append(None)
                continue

            # 根据数据类型进行转换
            col_type = columns_info[col_name]

            if col_type == "DATETIME":
                if isinstance(value, str):
                    try:
                        value = pd.to_datetime(value)
                    except:
                        value = None
                row_data.append(value)
            elif "INT" in col_type or "FLOAT" in col_type:
                try:
                    if "FLOAT" in col_type:
                        value = float(value)
                    else:
                        value = int(value)
                except:
                    value = None
                row_data.append(value)
            elif col_type == "BOOLEAN":
                if isinstance(value, str):
                    value = value.lower() in ['true', '1', 'yes', 'y', '是', '真']
                row_data.append(bool(value))
            else:
                # 字符串类型
                row_data.append(str(value))

        data_list.append(tuple(row_data))

    return data_list

def get_database_column_names(columns_info: Dict[str, str]) -> List[str]:
    """
    获取数据库中实际使用的列名（时间列统一为date）

    Args:
        columns_info: 列信息字典

    Returns:
        数据库列名列表
    """
    db_columns = []
    for col_name, col_type in columns_info.items():
        if col_type == "DATETIME":
            db_columns.append("date")
        else:
            db_columns.append(sanitize_column_name(col_name))
    return db_columns
