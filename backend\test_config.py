# test_config.py - 测试配置文件是否正确

import json
import os

def test_servers_config():
    """测试servers_config.json配置文件"""
    print("=== 测试 servers_config.json ===")
    
    try:
        with open("servers_config.json", "r", encoding="utf-8") as f:
            config = json.load(f)
        
        print("✅ JSON格式正确")
        
        # 检查MCP服务器配置
        mcp_servers = config.get("mcpServers", {})
        print(f"📊 配置的MCP服务器数量: {len(mcp_servers)}")
        
        for server_name, server_config in mcp_servers.items():
            print(f"  - {server_name}: {server_config.get('command')} {' '.join(server_config.get('args', []))}")
            
            # 检查文件是否存在（对于Python脚本）
            if server_config.get('command') == 'python' and server_config.get('args'):
                script_path = server_config['args'][0]
                if os.path.exists(script_path):
                    print(f"    ✅ 脚本文件存在: {script_path}")
                else:
                    print(f"    ❌ 脚本文件不存在: {script_path}")
        
        # 检查是否包含三峡调度服务器
        if "sanxiaScheduling" in mcp_servers:
            print("✅ 三峡调度服务器配置已添加")
            sch_config = mcp_servers["sanxiaScheduling"]
            print(f"   命令: {sch_config.get('command')}")
            print(f"   参数: {sch_config.get('args')}")
            print(f"   传输: {sch_config.get('transport')}")
        else:
            print("❌ 三峡调度服务器配置未找到")
            
    except json.JSONDecodeError as e:
        print(f"❌ JSON格式错误: {e}")
    except FileNotFoundError:
        print("❌ servers_config.json 文件不存在")
    except Exception as e:
        print(f"❌ 其他错误: {e}")

def test_agent_prompts():
    """测试agent_prompts.txt配置文件"""
    print("\n=== 测试 agent_prompts.txt ===")
    
    try:
        with open("agent_prompts.txt", "r", encoding="utf-8") as f:
            content = f.read()
        
        print("✅ 文件读取成功")
        print(f"📊 文件长度: {len(content)} 字符")
        
        # 检查是否包含三峡调度相关内容
        if "三峡水利调度" in content:
            print("✅ 包含三峡水利调度相关内容")
        else:
            print("❌ 未找到三峡水利调度相关内容")
            
        if "SanxiaSchedulingServer" in content:
            print("✅ 包含SanxiaSchedulingServer配置")
        else:
            print("❌ 未找到SanxiaSchedulingServer配置")
            
        if "sanxia_flood_routing" in content:
            print("✅ 包含sanxia_flood_routing工具说明")
        else:
            print("❌ 未找到sanxia_flood_routing工具说明")
            
        # 统计Agent数量
        agent_count = content.count("**") // 2  # 粗略估计
        print(f"📊 大约包含 {agent_count} 个配置项")
        
    except FileNotFoundError:
        print("❌ agent_prompts.txt 文件不存在")
    except Exception as e:
        print(f"❌ 其他错误: {e}")

def test_sch_mcp_file():
    """测试sch_mcp.py文件是否存在"""
    print("\n=== 测试 sch_mcp.py 文件 ===")
    
    sch_mcp_path = "./Langgraph_Learning/MCP/sch_mcp.py"
    
    if os.path.exists(sch_mcp_path):
        print(f"✅ 文件存在: {sch_mcp_path}")
        
        # 检查文件大小
        file_size = os.path.getsize(sch_mcp_path)
        print(f"📊 文件大小: {file_size} 字节")
        
        # 尝试导入检查语法
        try:
            import sys
            sys.path.append("./Langgraph_Learning/MCP")
            import sch_mcp
            print("✅ Python语法检查通过")
        except ImportError as e:
            print(f"⚠️  导入警告: {e}")
        except SyntaxError as e:
            print(f"❌ 语法错误: {e}")
        except Exception as e:
            print(f"⚠️  其他导入问题: {e}")
    else:
        print(f"❌ 文件不存在: {sch_mcp_path}")

if __name__ == "__main__":
    print("🔍 配置文件测试工具")
    print("=" * 50)
    
    test_servers_config()
    test_agent_prompts()
    test_sch_mcp_file()
    
    print("\n" + "=" * 50)
    print("✅ 测试完成！")
    print("\n💡 如果所有测试都通过，说明配置已正确添加。")
    print("💡 现在可以启动Agent并测试三峡水利调度功能。")
