#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
通用Excel数据导入MySQL主程序
支持命令行参数和交互式配置
"""

import sys
import os
import argparse
from pathlib import Path
import logging
from typing import Optional

# 添加当前目录到Python路径
current_dir = Path(__file__).parent
sys.path.insert(0, str(current_dir))

from universal_excel_importer import UniversalExcelImporter
from db_config import DB_CONFIG, EXCEL_FILE_PATH, LOG_CONFIG

# 配置日志
logging.basicConfig(
    level=getattr(logging, LOG_CONFIG['level']),
    format=LOG_CONFIG['format']
)
logger = logging.getLogger(__name__)

def check_requirements() -> bool:
    """检查运行环境和依赖"""
    print("正在检查运行环境...")
    
    # 检查必要的Python包
    required_packages = ['pandas', 'mysql.connector', 'openpyxl']
    missing_packages = []
    
    for package in required_packages:
        try:
            if package == 'mysql.connector':
                import mysql.connector
            else:
                __import__(package)
            print(f"✅ {package} 已安装")
        except ImportError:
            missing_packages.append(package)
            print(f"❌ {package} 未安装")
    
    if missing_packages:
        print(f"\n请安装缺失的包:")
        for package in missing_packages:
            if package == 'mysql.connector':
                print("pip install mysql-connector-python")
            else:
                print(f"pip install {package}")
        return False
    
    return True

def get_excel_path_from_user() -> Optional[str]:
    """从用户输入获取Excel文件路径"""
    while True:
        excel_path = input(f"请输入Excel文件路径 (默认: {EXCEL_FILE_PATH}): ").strip()
        
        if not excel_path:
            excel_path = EXCEL_FILE_PATH
        
        # 支持相对路径
        if not os.path.isabs(excel_path):
            excel_path = os.path.join(current_dir.parent, excel_path)
        
        if os.path.exists(excel_path):
            print(f"✅ Excel文件存在: {excel_path}")
            return excel_path
        else:
            print(f"❌ Excel文件不存在: {excel_path}")
            retry = input("是否重新输入路径？(y/n): ").lower().strip()
            if retry != 'y':
                return None

def show_config_info(excel_path: str):
    """显示配置信息"""
    print("\n" + "="*60)
    print("配置信息")
    print("="*60)
    
    print(f"Excel文件路径: {excel_path}")
    print(f"数据库主机: {DB_CONFIG['host']}")
    print(f"数据库端口: {DB_CONFIG['port']}")
    print(f"数据库名称: {DB_CONFIG['database']}")
    print(f"用户名: {DB_CONFIG['user']}")
    print(f"密码: {'*' * len(DB_CONFIG['password']) if DB_CONFIG['password'] else '(未设置)'}")

def preview_excel_sheets(excel_path: str):
    """预览Excel文件的Sheet信息"""
    try:
        import pandas as pd
        excel_file = pd.ExcelFile(excel_path)
        sheets = excel_file.sheet_names
        
        print(f"\n发现 {len(sheets)} 个Sheet:")
        print("-" * 40)
        
        for i, sheet_name in enumerate(sheets, 1):
            # 读取前几行数据预览
            try:
                df = pd.read_excel(excel_path, sheet_name=sheet_name, nrows=3)
                print(f"{i}. Sheet名: '{sheet_name}'")
                print(f"   行数: {len(df)} (预览)")
                print(f"   列数: {len(df.columns)}")
                print(f"   列名: {list(df.columns)}")
                
                # 生成的表名预览
                from utils import sanitize_table_name
                table_name = sanitize_table_name(sheet_name)
                print(f"   将创建表: '{table_name}'")
                print()
                
            except Exception as e:
                print(f"{i}. Sheet名: '{sheet_name}' (读取失败: {e})")
                print()
        
    except Exception as e:
        print(f"预览Excel文件失败: {e}")

def main():
    """主函数"""
    parser = argparse.ArgumentParser(description='通用Excel数据导入MySQL程序')
    parser.add_argument('--excel', '-e', type=str, help='Excel文件路径')
    parser.add_argument('--preview', '-p', action='store_true', help='仅预览Excel文件结构，不导入数据')
    parser.add_argument('--verify', '-v', action='store_true', help='导入后验证数据')
    parser.add_argument('--quiet', '-q', action='store_true', help='静默模式，减少输出')
    
    args = parser.parse_args()
    
    if not args.quiet:
        print("="*60)
        print("通用Excel数据导入MySQL程序")
        print("="*60)
    
    # 检查运行环境
    if not check_requirements():
        print("\n❌ 环境检查失败，请解决上述问题后重试")
        sys.exit(1)
    
    # 获取Excel文件路径
    if args.excel:
        excel_path = args.excel
        if not os.path.exists(excel_path):
            print(f"❌ 指定的Excel文件不存在: {excel_path}")
            sys.exit(1)
    else:
        excel_path = get_excel_path_from_user()
        if not excel_path:
            print("❌ 未指定有效的Excel文件路径")
            sys.exit(1)
    
    # 显示配置信息
    if not args.quiet:
        show_config_info(excel_path)
    
    # 预览模式
    if args.preview:
        preview_excel_sheets(excel_path)
        return
    
    # 确认是否继续
    if not args.quiet:
        print("\n" + "="*60)
        print("准备开始导入数据")
        print("="*60)
        
        # 预览Sheet信息
        preview_excel_sheets(excel_path)
        
        confirm = input("确认要开始导入数据吗？(y/n): ").lower().strip()
        if confirm != 'y':
            print("操作已取消")
            sys.exit(0)
    
    # 创建导入器并执行导入
    print("\n开始导入数据...")
    try:
        importer = UniversalExcelImporter(excel_path=excel_path)
        
        # 执行导入
        success = importer.import_all_sheets()
        
        if success:
            print("\n🎉 数据导入完成！")
            
            # 验证导入结果
            if args.verify or (not args.quiet and 
                             input("是否验证导入结果？(y/n): ").lower().strip() == 'y'):
                print("\n开始验证导入结果...")
                importer.verify_import()
        else:
            print("\n❌ 数据导入失败，请检查日志")
            sys.exit(1)
            
    except KeyboardInterrupt:
        print("\n\n⚠️  用户中断操作")
        sys.exit(1)
    except Exception as e:
        print(f"\n❌ 导入过程中发生错误: {e}")
        logger.exception("导入过程异常")
        sys.exit(1)

if __name__ == "__main__":
    main()
