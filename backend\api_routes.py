"""
API路由处理器
处理前端的Word文档上传和处理请求
"""

import os
import json
import asyncio
import logging
from pathlib import Path
from typing import Dict, Any, Optional
from fastapi import FastAPI, File, UploadFile, HTTPException
from fastapi.responses import FileResponse
from fastapi.middleware.cors import CORSMiddleware
from pydantic import BaseModel
from docx import Document as DocxDocument
import uvicorn
import mammoth
from mammoth import images as mammoth_images
from bs4 import BeautifulSoup
import base64
from graph import make_graph

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# 创建FastAPI应用
app = FastAPI(title="Word编辑Agent API", version="1.0.0")

# 添加CORS中间件
app.add_middleware(
    CORSMiddleware,
    allow_origins=["http://localhost:3000"],  # 前端地址
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# 全局变量存储Agent实例
agent = None
upload_dir = Path("uploads")
upload_dir.mkdir(exist_ok=True)
edits_dir = upload_dir / ".edits"
edits_dir.mkdir(exist_ok=True)

class LoadDocumentRequest(BaseModel):
    file_path: str
    max_tokens_per_section: int = 30000

class SelectSectionRequest(BaseModel):
    document_id: str
    section_id: str

class EditSectionRequest(BaseModel):
    operation: str
    content: str = ""
    format_options: Dict[str, Any] = {}

class UpdateSectionRequest(BaseModel):
    file_path: str
    section_id: str
    new_html: str

class AddSectionRequest(BaseModel):
    file_path: str
    new_section_id: str
    title: str
    level: int = 2
    html: str = "<p>新章节内容</p>"
    insert_after_section_id: Optional[str] = None

@app.on_event("startup")
async def startup_event():
    """启动时初始化Agent"""
    global agent
    try:
        logger.info("初始化LangGraph Agent...")
        agent = await make_graph()
        logger.info("Agent初始化成功")
    except Exception as e:
        logger.error(f"Agent初始化失败: {e}")
        raise

@app.post("/api/word/upload")
async def upload_word_document(file: UploadFile = File(...)):
    """上传Word文档"""
    try:
        # 验证文件类型（仅支持 .docx）
        if not file.filename or not file.filename.lower().endswith('.docx'):
            raise HTTPException(status_code=400, detail="仅支持 .docx 格式的Word文档")
        
        # 保存文件
        file_path = upload_dir / file.filename
        
        with open(file_path, "wb") as buffer:
            content = await file.read()
            buffer.write(content)
        
        logger.info(f"文件上传成功: {file_path}")
        
        return {
            "success": True,
            "message": "文件上传成功",
            "filePath": str(file_path),
            "fileName": file.filename,
            "fileSize": len(content)
        }
        
    except Exception as e:
        logger.error(f"文件上传失败: {e}")
        raise HTTPException(status_code=500, detail=f"文件上传失败: {str(e)}")

def _heading_level(style_name: str) -> int | None:
    name = (style_name or "").lower()
    # 支持英文与中文常见标题样式
    mapping = {
        "heading 1": 1, "heading 2": 2, "heading 3": 3, "heading 4": 4,
        "heading 5": 5, "heading 6": 6, "标题 1": 1, "标题 2": 2, "标题 3": 3,
        "标题 4": 4, "标题 5": 5, "标题 6": 6,
    }
    # 直接匹配
    if name in mapping:
        return mapping[name]
    # 某些模板可能是 Heading1/标题1 无空格
    for prefix in ["heading", "标题"]:
        if name.startswith(prefix):
            try:
                num = int(name.replace(prefix, "").strip())
                if 1 <= num <= 6:
                    return num
            except ValueError:
                pass
    return None

def convert_image_to_base64(image) -> dict:
    """将 mammoth 图片对象转换为 Base64 内联 Data URI。"""
    with image.open() as image_bytes:
        encoded_src = base64.b64encode(image_bytes.read()).decode("ascii")
    return {"src": f"data:{image.content_type};base64,{encoded_src}"}


def _parse_word_sections(file_path: str) -> dict:
    # 兼容多种路径格式：绝对/相对、URL式 /uploads/xxx、Windows 反斜杠
    raw = file_path.strip().strip('"').strip("'")
    path = Path(raw)
    if not path.exists():
        # 处理 URL 风格路径，例如 /uploads/xxx 或 uploads/xxx
        cleaned = raw.lstrip("/\\")
        candidate = Path.cwd() / cleaned
        if candidate.exists():
            path = candidate
        else:
            raise HTTPException(status_code=404, detail=f"文件不存在: {raw}")

    # 读取（或初始化）该文档的编辑覆盖缓存
    doc_key = f"doc_{abs(hash(str(path))) }"
    edits_path = edits_dir / f"{doc_key}.json"
    edits: Dict[str, Any] = {}
    if edits_path.exists():
        try:
            edits = json.loads(edits_path.read_text(encoding="utf-8"))
        except Exception:
            edits = {}

    # 使用 mammoth 将整篇 DOCX 转为 HTML（包含表格与图片，图片以内联 data URI 形式）
    # full_html: str = ""
    # mammoth_error: Optional[Exception] = None
    # try:
    #     with open(path, "rb") as docx_file:
    #         result = mammoth.convert_to_html(
    #             docx_file,
    #             convert_image=mammoth_images.inline(),
    #             style_map=(
    #                 "p[style-name='Title'] => h1:fresh\n"
    #                 "p[style-name^='Heading'] => h2:fresh\n"
    #             ),
    #         )
    #         full_html = result.value or ""
    # except Exception as e:
    #     mammoth_error = e
    #     logger.warning(f"mammoth 转换失败，将使用 python-docx 回退解析: {e}")

    full_html: str = ""
    mammoth_error: Optional[Exception] = None
    try:
        with open(path, "rb") as docx_file:
            # 修改 style_map 的赋值方式
            result = mammoth.convert_to_html(
                docx_file,
                convert_image=mammoth_images.img_element(convert_image_to_base64),
                style_map="p[style-name='Title'] => h1:fresh\n"
                          "p[style-name^='Heading'] => h2:fresh"
            )
            full_html = result.value or ""
    except Exception as e:
        mammoth_error = e
        # 注意：这里的日志可以打印更详细的错误，方便调试
        import traceback
        logger.warning(f"mammoth 转换失败，将使用 python-docx 回退解析: {e}\n{traceback.format_exc()}")

    # 如果 mammoth 失败或未产出任何 HTML，则回退到 python-docx 提取最小可用 HTML
    if not full_html:
        try:
            doc = DocxDocument(path)
            html_chunks: list[str] = []
            for paragraph in doc.paragraphs:
                style_name = getattr(paragraph.style, "name", "") if paragraph.style else ""
                level = _heading_level(style_name) or None
                text = paragraph.text or ""
                if level:
                    html_chunks.append(f"<h{level}>{text}</h{level}>")
                else:
                    html_chunks.append(f"<p>{text}</p>")
            full_html = "".join(html_chunks)
            logger.info("已使用 python-docx 回退解析生成 HTML。")
        except Exception as e2:
            err = mammoth_error or e2
            raise HTTPException(status_code=500, detail=f"无法读取文档: {err}")

    # 用 BeautifulSoup 按标题拆分章节，保留原始格式（表格、图片等）
    soup = BeautifulSoup(full_html, "html.parser")
    # mammoth 输出通常没有 <body> 标签，这里直接使用 soup 根节点的子元素作为遍历对象
    body = soup
    # 收集所有节点，按遇到的 h1-h6 分割
    sections: list[dict] = []
    current_block_nodes: list = []
    current_meta: Optional[dict] = None
    sec_index = 0

    def push_section():
        nonlocal current_block_nodes, current_meta
        if current_meta is None:
            return
        # 组合HTML
        container = BeautifulSoup("<div></div>", "html.parser")
        div = container.div
        for node in current_block_nodes:
            div.append(node)
        html_content = str(div)
        text_content = BeautifulSoup(html_content, "html.parser").get_text("\n").strip()
        char_count = sum(1 for ch in text_content if not ch.isspace())

        # 应用编辑覆盖（如果存在）
        section_id = current_meta["id"]
        override_html = edits.get(section_id, {}).get("html") if isinstance(edits, dict) else None
        final_html = override_html if override_html else html_content
        final_text = BeautifulSoup(final_html, "html.parser").get_text("\n").strip()
        char_count = sum(1 for ch in final_text if not ch.isspace())

        sections.append({
            **current_meta,
            "content": final_text,
            "html": final_html,
            "word_count": char_count,
            "char_count": char_count,
        })
        current_block_nodes = []
        current_meta = None

    # 遍历顶级子节点；某些情况下 BeautifulSoup 会把字符串与空白混入，先过滤
    for node in [n for n in body.children]:
        if not getattr(node, "name", None):
            # 文本节点等，直接收集
            if current_meta is None:
                # 尚未遇到标题，视作“正文”
                sec_index += 1
                current_meta = {"id": f"h1-{sec_index}", "title": "正文", "level": 1}
            current_block_nodes.append(node)
            continue
        tag_name = node.name.lower()
        if tag_name in {"h1", "h2", "h3", "h4", "h5", "h6"}:
            # 遇到新的标题，推送上一节
            push_section()
            sec_index += 1
            level = int(tag_name[1])
            title_text = node.get_text(strip=True)
            current_meta = {"id": f"h{level}-{sec_index}", "title": title_text or f"无标题-{sec_index}", "level": level}
        else:
            if current_meta is None:
                sec_index += 1
                current_meta = {"id": f"h1-{sec_index}", "title": "正文", "level": 1}
            current_block_nodes.append(node)

    # 推送最后一节
    push_section()

    # 处理待插入的新章节
    if isinstance(edits, dict) and "_new_sections" in edits:
        new_section_list = edits.get("_new_sections", [])
        if isinstance(new_section_list, list):
            for new_sec_info in new_section_list:
                if not all(k in new_sec_info for k in ["id", "title", "level", "html"]):
                    continue
                
                insert_after_id = new_sec_info.get("insert_after")
                new_section_obj = {
                    "id": new_sec_info["id"],
                    "title": new_sec_info["title"],
                    "level": new_sec_info["level"],
                    "html": new_sec_info["html"],
                    "content": BeautifulSoup(new_sec_info["html"], "html.parser").get_text("\n").strip(),
                    "word_count": sum(1 for ch in BeautifulSoup(new_sec_info["html"], "html.parser").get_text() if not ch.isspace()),
                    "char_count": sum(1 for ch in BeautifulSoup(new_sec_info["html"], "html.parser").get_text() if not ch.isspace()),
                }

                if insert_after_id:
                    try:
                        idx = next(i for i, s in enumerate(sections) if s["id"] == insert_after_id)
                        sections.insert(idx + 1, new_section_obj)
                    except StopIteration:
                        sections.append(new_section_obj) # 未找到，追加到末尾
                else:
                    sections.append(new_section_obj)
    
    if not sections:
        # 文档可能没有检测到任何标题，兜底将全文作为一节
        only_html = full_html
        only_text = BeautifulSoup(only_html, "html.parser").get_text("\n").strip()
        char_count = sum(1 for ch in only_text if not ch.isspace())
        sections.append({
            "id": "h1-1",
            "title": "正文",
            "level": 1,
            "content": only_text,
            "html": only_html,
            "word_count": char_count,
            "char_count": char_count,
        })

    total_words = sum(s.get("word_count", 0) for s in sections)
    return {
        "success": True,
        "document_id": doc_key,
        "message": f"文档加载和分割成功，共 {len(sections)} 个章节",
        "document": {
            "name": path.name,
            "sections": sections,
            "total_words": total_words,
            "total_tokens": total_words,
        },
    }

@app.post("/api/word/load-and-split")
async def load_and_split_document(request: LoadDocumentRequest):
    """加载并分割Word文档（基于 python-docx 解析章节与内容）"""
    try:
        result = _parse_word_sections(request.file_path)
        logger.info(f"文档加载成功: {request.file_path} -> {len(result['document']['sections'])} 节")
        return result
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"文档加载失败: {e}")
        raise HTTPException(status_code=500, detail=f"文档加载失败: {str(e)}")

@app.post("/api/word/select-section")
async def select_section_for_editing(request: SelectSectionRequest):
    """选择章节进行编辑（前端已内置编辑，无需调用后端Agent，接口保留兼容）。"""
    try:
        return {
            "success": True,
            "message": f"已选择章节 {request.section_id} (前端本地编辑模式)",
            "document_id": request.document_id,
            "section_id": request.section_id
        }
    except Exception as e:
        logger.error(f"选择章节失败: {e}")
        raise HTTPException(status_code=500, detail=f"选择章节失败: {str(e)}")

@app.post("/api/word/edit-section")
async def edit_current_section(request: EditSectionRequest):
    """兼容占位：前端直编直存，无需后端代理。"""
    try:
        return {
            "success": True,
            "message": f"操作 {request.operation} 已接收（前端本地编辑模式）",
            "operation": request.operation
        }
    except Exception as e:
        logger.error(f"章节编辑失败: {e}")
        raise HTTPException(status_code=500, detail=f"章节编辑失败: {str(e)}")


@app.post("/api/word/update-section")
async def update_section_html(req: UpdateSectionRequest):
    """直接更新某章节的HTML内容（供前端手动编辑或Agent调用）。
    将变更写入临时编辑缓存，前端刷新后即可看到效果。
    """
    try:
        path = Path(req.file_path)
        if not path.exists():
            raise HTTPException(status_code=404, detail="文件不存在")
        doc_key = f"doc_{abs(hash(str(path))) }"
        edits_path = edits_dir / f"{doc_key}.json"
        if edits_path.exists():
            try:
                data = json.loads(edits_path.read_text(encoding="utf-8"))
            except Exception:
                data = {}
        else:
            data = {}
        if not isinstance(data, dict):
            data = {}
        if req.section_id not in data:
            data[req.section_id] = {}
        data[req.section_id]["html"] = req.new_html
        edits_path.write_text(json.dumps(data, ensure_ascii=False, indent=2), encoding="utf-8")
        logger.info(f"章节 {req.section_id} HTML 已更新并缓存: {edits_path}")
        return {"success": True, "message": "章节内容已更新"}
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"更新章节失败: {e}")
        raise HTTPException(status_code=500, detail=f"更新章节失败: {str(e)}")

@app.post("/api/word/add-section")
async def add_section(req: AddSectionRequest):
    """在Word文档中添加一个新的章节。"""
    try:
        path = Path(req.file_path)
        if not path.exists():
            raise HTTPException(status_code=404, detail="文件不存在")

        doc_key = f"doc_{abs(hash(str(path)))}"
        edits_path = edits_dir / f"{doc_key}.json"
        
        if edits_path.exists():
            try:
                data = json.loads(edits_path.read_text(encoding="utf-8"))
            except Exception:
                data = {}
        else:
            data = {}
        
        if not isinstance(data, dict):
            data = {}
        
        # 存储新章节信息以供后续处理
        if "_new_sections" not in data:
            data["_new_sections"] = []
        
        new_section_info = {
            "id": req.new_section_id,
            "title": req.title,
            "level": req.level,
            "html": req.html,
            "insert_after": req.insert_after_section_id,
        }
        data["_new_sections"].append(new_section_info)

        edits_path.write_text(json.dumps(data, ensure_ascii=False, indent=2), encoding="utf-8")
        logger.info(f"新增章节 '{req.title}' 的请求已缓存: {edits_path}")
        
        return {"success": True, "message": "添加章节的请求已接收", "section_id": req.new_section_id}
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"添加章节失败: {e}")
        raise HTTPException(status_code=500, detail=f"添加章节失败: {str(e)}")

@app.post("/api/word/save-document")
async def save_document():
    """兼容占位：当前保存采用分节缓存文件（/uploads/.edits/*.json），无需显式保存。"""
    try:
        return {"success": True, "message": "已保存（前端触发，后端缓存已更新）"}
    except Exception as e:
        logger.error(f"文档保存失败: {e}")
        raise HTTPException(status_code=500, detail=f"文档保存失败: {str(e)}")

@app.get("/api/health")
async def health_check():
    """健康检查"""
    return {
        "status": "healthy",
        "agent_ready": agent is not None,
        "message": "Word编辑Agent API运行正常"
    }

@app.get("/api/word/download")
async def download_word_document(file_path: str):
    """下载完整Word文档"""
    try:
        path = Path(file_path)
        if not path.exists() or not path.is_file():
            raise HTTPException(status_code=404, detail="文件不存在")

        file_name = path.name
        ext = path.suffix.lower()
        if ext == ".docx":
            media_type = "application/vnd.openxmlformats-officedocument.wordprocessingml.document"
        elif ext == ".doc":
            media_type = "application/msword"
        else:
            media_type = "application/octet-stream"

        return FileResponse(
            path=path,
            filename=file_name,
            media_type=media_type,
        )
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"文件下载失败: {str(e)}")

if __name__ == "__main__":
    # 启动API服务器
    uvicorn.run(
        "api_routes:app",
        host="0.0.0.0",
        port=8001,
        reload=True,
        log_level="info"
    )
