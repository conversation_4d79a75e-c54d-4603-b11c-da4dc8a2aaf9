# example_agent_with_sch_mcp.py - 集成三峡调度MCP的LangGraph Agent示例

import asyncio
import os

from dotenv import load_dotenv
from langchain_mcp_adapters.client import MultiServerMCPClient
from langgraph.prebuilt import create_react_agent
from langchain.chat_models import init_chat_model
from langchain_deepseek import ChatDeepSeek

load_dotenv()

async def create_scheduling_agent():
    """创建集成了三峡调度MCP的Agent"""
    
    # 获取当前文件的目录
    current_dir = os.path.dirname(os.path.abspath(__file__))
    sch_mcp_path = os.path.join(current_dir, "sch_mcp.py")
    
    # 配置MCP客户端
    client = MultiServerMCPClient({
        "scheduling": {
            "command": "python",
            "args": [sch_mcp_path],
            "transport": "stdio"
        }
    })
    
    # 获取工具
    tools = await client.get_tools()
    print(f"成功加载 {len(tools)} 个调度工具:")
    for tool in tools:
        print(f"  - {tool.name}: {tool.description}")
    
    # 创建Agent
    # model = init_chat_model("anthropic:claude-3-5-sonnet-latest")
    # model = init_chat_model(model="deepseek-chat", model_provider="deepseek")
    model = ChatDeepSeek(model="deepseek-chat")
    agent = create_react_agent(model, tools)
    
    return agent

async def demo_scheduling_queries():
    """演示各种调度查询"""
    
    print("正在创建三峡调度Agent...")
    agent = await create_scheduling_agent()
    
    # 示例查询列表
    queries = [
        "获取三峡调度模型的默认参数配置",
        "列出所有可用的洪水场景数据",
        "验证1954年0.01频率的洪水数据是否可用",
        "计算1954年0.01频率的洪水调度，使用默认参数",
        "计算1954年0.01频率的洪水调度，初始水位设为165米，上游最大动用设为80",
        "同时计算1954年的0.01和0.001两个频率的洪水调度"
    ]
    
    for i, query in enumerate(queries, 1):
        print(f"\n{'='*60}")
        print(f"示例 {i}: {query}")
        print('='*60)
        
        try:
            response = await agent.ainvoke({
                "messages": [{"role": "user", "content": query}]
            })
            
            # 提取最后一条消息的内容
            last_message = response["messages"][-1]
            print(f"Agent回复: {last_message.content}")
            
        except Exception as e:
            print(f"查询失败: {e}")
        
        # 添加延迟避免过快请求
        await asyncio.sleep(1)

async def interactive_mode():
    """交互模式"""
    print("正在启动交互模式...")
    agent = await create_scheduling_agent()
    
    print("\n三峡调度Agent已就绪！")
    print("您可以询问以下类型的问题:")
    print("- 获取默认参数")
    print("- 列出可用场景")
    print("- 验证数据可用性")
    print("- 执行调度计算")
    print("输入 'quit' 退出\n")
    
    while True:
        try:
            user_input = input("您的问题: ").strip()
            
            if user_input.lower() in ['quit', 'exit', '退出']:
                print("再见！")
                break
            
            if not user_input:
                continue
            
            print("正在处理...")
            response = await agent.ainvoke({
                "messages": [{"role": "user", "content": user_input}]
            })
            
            last_message = response["messages"][-1]
            print(f"\nAgent回复:\n{last_message.content}\n")
            
        except KeyboardInterrupt:
            print("\n\n程序被用户中断")
            break
        except Exception as e:
            print(f"处理失败: {e}\n")

async def main():
    """主函数"""
    print("三峡水利调度 LangGraph Agent 示例")
    print("="*50)
    
    mode = input("选择模式 (1: 演示模式, 2: 交互模式): ").strip()
    
    if mode == "1":
        await demo_scheduling_queries()
    elif mode == "2":
        await interactive_mode()
    else:
        print("无效选择，运行演示模式...")
        await demo_scheduling_queries()

if __name__ == "__main__":
    # 注意：这个示例需要配置有效的API密钥
    # 请确保设置了相应的环境变量，如 ANTHROPIC_API_KEY
    
    try:
        asyncio.run(main())
    except KeyboardInterrupt:
        print("\n程序被用户中断")
    except Exception as e:
        print(f"程序运行失败: {e}")
        print("请检查:")
        print("1. 是否安装了所需依赖: pip install langchain-mcp-adapters")
        print("2. 是否配置了API密钥")
        print("3. 是否有访问flood_control模块的权限")
